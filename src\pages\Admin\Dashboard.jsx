import React, { useState, useEffect } from 'react';
import { <PERSON>, Col, Card, Alert, Button, Badge } from 'react-bootstrap';
import { adminAPI } from '../../services/api';
import { Users, BookOpen, UserCheck, Activity, Plus, Settings, Shield, Eye } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';

const Dashboard = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await adminAPI.getStatistics();
        setStats(response.data);
      } catch (err) {
        setError('فشل في تحميل الإحصائيات');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats?.totalUsers || 0,
      icon: Users,
      variant: 'primary'
    },
    {
      title: 'إجمالي الدورات',
      value: stats?.totalCourses || 0,
      icon: BookOpen,
      variant: 'success'
    },
    {
      title: 'نشط اليوم',
      value: stats?.activeToday || 0,
      icon: Activity,
      variant: 'warning'
    },
    {
      title: 'المديرون',
      value: stats?.totalAdmins || 0,
      icon: UserCheck,
      variant: 'info'
    }
  ];

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold">لوحة التحكم</h2>
          <div className="d-flex align-items-center gap-2">
            <Badge bg={isAdmin ? 'danger' : 'primary'} className="d-flex align-items-center gap-1">
              <Shield size={14} />
              {isAdmin ? 'مدير عام' : 'مدير'}
            </Badge>
            <span className="text-muted">مرحباً، {user?.name}</span>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Row className="g-4 mb-5">
        {statCards.map((stat, index) => (
          <Col key={stat.title} sm={6} lg={3}>
            <Card className="border-0 shadow-sm h-100">
              <Card.Body className="text-center">
                <stat.icon size={48} className={`text-${stat.variant} mb-3`} />
                <h3 className="fw-bold mb-1">{Number(stat.value || 0).toLocaleString()}</h3>
                <p className="text-muted mb-0">{stat.title}</p>
              </Card.Body>
            </Card>
          </Col>
        ))}
      </Row>

      <Row className="g-4">
        <Col lg={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0">
              <h5 className="fw-bold mb-0">إجراءات سريعة</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-column gap-2">
                {/* Admin Actions */}
                <Button variant="outline-primary" href="/admin/add-course" className="d-flex align-items-center">
                  <Plus size={16} className="me-2" />
                  إضافة دورة جديدة
                </Button>

                <Button variant="outline-info" href="/admin/courses" className="d-flex align-items-center">
                  <BookOpen size={16} className="me-2" />
                  إدارة الدورات
                </Button>

                <Button variant="outline-secondary" href="/admin/courses" className="d-flex align-items-center">
                  <Eye size={16} className="me-2" />
                  عرض جميع الدورات
                </Button>

                {/* Super Admin Only Actions */}
                {isAdmin && (
                  <>
                    <hr className="my-2" />
                    <small className="text-muted fw-bold">صلاحيات المدير العام</small>

                    <Button variant="outline-success" href="/admin/users" className="d-flex align-items-center">
                      <Users size={16} className="me-2" />
                      إدارة المستخدمين
                    </Button>

                    <Button variant="outline-warning" className="d-flex align-items-center" title='قريبا'>
                      <Settings size={16} className="me-2" />
                      إعدادات النظام
                    </Button>
                  </>
                )}
              </div>
            </Card.Body>
          </Card>
        </Col>
        
        <Col lg={6}>
          <Card className="border-0 shadow-sm h-100">
            <Card.Header className="bg-white border-0">
              <h5 className="fw-bold mb-0">نظرة عامة على المنصة</h5>
            </Card.Header>
            <Card.Body>
              {/* عرض الاحصائيات */}
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                    <span className="text-muted">مشاركة المستخدمين</span>
                    <span className="fw-bold">
                      {stats && stats.totalUsers > 0 ? Math.round((stats.activeToday / stats.totalUsers) * 100) : 0}%
                    </span>
                  </div>
                  <div className="progress" style={{ height: '8px' }}>
                    <div 
                      className="progress-bar bg-success" 
                      style={{ width: `${stats && stats.totalUsers > 0 ? (stats.activeToday / stats.totalUsers) * 100 : 0}%` }}
                    ></div>
                  </div>
                  </div>
              
             <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <span className="text-muted">تغطية المديرين</span>
                  <span className="fw-bold">
                    {stats && stats.totalAdmins > 0 ? Math.round((stats.totalAdmins / stats.totalAdmins) * 100) : 0}%
                  </span>
                </div>
                <div className="progress" style={{ height: '8px' }}>
                  <div 
                    className="progress-bar bg-warning" 
                    style={{ width: `${stats && stats.totalAdmins > 0 ? (stats.totalAdmins / stats.totalAdmins) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>

              <small className="text-muted">
                مقاييس صحة المنصة محدثة في الوقت الفعلي
              </small>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;