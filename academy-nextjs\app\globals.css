/* Global Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .me-1,
[dir="rtl"] .me-2,
[dir="rtl"] .me-3 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

[dir="rtl"] .ms-1,
[dir="rtl"] .ms-2,
[dir="rtl"] .ms-3 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

/* Theme Support */
[data-bs-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #dee2e6;
}

[data-bs-theme="light"] {
  --bs-body-bg: #f8f9fa;
  --bs-body-color: #212529;
}

/* Custom Components */
.hover-shadow {
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading Spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Course Cards */
.course-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.course-image {
  height: 200px;
  object-fit: cover;
  width: 100%;
}

/* Buttons */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
  color: white;
}

/* Forms */
.form-floating > label {
  right: 0.75rem;
  left: auto;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .course-card {
    margin-bottom: 1rem;
  }
}

/* Dark mode specific styles */
[data-bs-theme="dark"] .course-card {
  background-color: #2d3748;
  border-color: #4a5568;
}

[data-bs-theme="dark"] .navbar {
  background-color: #1a202c !important;
}

[data-bs-theme="dark"] .card {
  background-color: #2d3748;
  border-color: #4a5568;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
