// API Configuration - حل بسيط وآمن
const getBaseUrl = () => {
  // في بيئة التطوير - كشف تلقائي
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;

    // إذا كان localhost أو 127.0.0.1
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return 'http://localhost:5000';
    }

    // إذا كان هناك متغير في window
    if (window.REACT_APP_API_URL) {
      return window.REACT_APP_API_URL;
    }

  }

  // fallback للإنتاج
  return 'https://academy-project-backend.onrender.com/';
};

// تصدير التكوين
export const API_CONFIG = {
  BASE_URL: getBaseUrl(),

  // API endpoints
  ENDPOINTS: {
    API: '/api',
    AUTH: '/api/auth',
    COURSES: '/api/courses',
    USERS: '/api/user',
    ADMIN: '/api/admin',
    SUPER_ADMIN: '/api/super-admin',
    CHATBOT: '/api/chatbot',
    HONOR_BOARD: '/api/honor-board',
    USER_PROGRESS: '/api/user-progress',
    PUBLIC: '/api/public'
  }
};

// Helper functions
export const getApiUrl = (endpoint = '') => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

export const getEndpointUrl = (endpointKey) => {
  const endpoint = API_CONFIG.ENDPOINTS[endpointKey];
  return endpoint ? `${API_CONFIG.BASE_URL}${endpoint}` : API_CONFIG.BASE_URL;
};

export default API_CONFIG;
