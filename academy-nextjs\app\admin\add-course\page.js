'use client';

import { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Badge } from 'react-bootstrap';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { 
  BookOpen, 
  Save, 
  ArrowLeft,
  Plus,
  Trash2,
  Upload,
  User,
  Clock,
  Target,
  Tag
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function AddCourse() {
  const { user, isAdminOrSuperAdmin, api } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    duration: '',
    level: 'مبتدئ',
    category: 'البرمجة',
    tags: [],
    price: 0,
    units: []
  });
  const [currentTag, setCurrentTag] = useState('');

  // التحقق من الصلاحيات
  if (!isAdminOrSuperAdmin) {
    router.push('/');
    return null;
  }

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
    
    if (error) setError('');
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addUnit = () => {
    setFormData(prev => ({
      ...prev,
      units: [...prev.units, {
        title: '',
        description: '',
        lessons: [],
        order: prev.units.length
      }]
    }));
  };

  const updateUnit = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      units: prev.units.map((unit, i) => 
        i === index ? { ...unit, [field]: value } : unit
      )
    }));
  };

  const removeUnit = (index) => {
    setFormData(prev => ({
      ...prev,
      units: prev.units.filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      setError('عنوان الدورة مطلوب');
      return false;
    }
    if (!formData.description.trim()) {
      setError('وصف الدورة مطلوب');
      return false;
    }
    if (!formData.instructor.trim()) {
      setError('اسم المدرب مطلوب');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('Form submitted!');
      console.log('Course data:', formData);
      
      const response = await api.post('/courses', formData);
      
      console.log('Response:', response.data);
      toast.success('تم إنشاء الدورة بنجاح!');
      router.push('/admin');
      
    } catch (error) {
      console.error('Error creating course:', error);
      const errorMessage = error.response?.data?.message || 'فشل في إنشاء الدورة';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex align-items-center mb-3">
              <Button
                variant="outline-secondary"
                onClick={() => router.back()}
                className="me-3"
              >
                <ArrowLeft size={16} />
              </Button>
              <div>
                <h1 className="display-6 fw-bold mb-0">إضافة دورة جديدة</h1>
                <p className="text-muted mb-0">أنشئ دورة تعليمية جديدة</p>
              </div>
            </div>
          </Col>
        </Row>

        {error && (
          <Row className="mb-4">
            <Col>
              <Alert variant="danger">{error}</Alert>
            </Col>
          </Row>
        )}

        <Form onSubmit={handleSubmit}>
          <Row>
            {/* Basic Information */}
            <Col lg={8}>
              <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">
                    <BookOpen size={20} className="me-2" />
                    المعلومات الأساسية
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={12}>
                      <Form.Group className="mb-3">
                        <Form.Label>عنوان الدورة *</Form.Label>
                        <Form.Control
                          type="text"
                          name="title"
                          value={formData.title}
                          onChange={handleInputChange}
                          placeholder="أدخل عنوان الدورة"
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={12}>
                      <Form.Group className="mb-3">
                        <Form.Label>وصف الدورة *</Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={4}
                          name="description"
                          value={formData.description}
                          onChange={handleInputChange}
                          placeholder="أدخل وصف مفصل للدورة"
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>
                          <User size={16} className="me-1" />
                          اسم المدرب *
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="instructor"
                          value={formData.instructor}
                          onChange={handleInputChange}
                          placeholder="أدخل اسم المدرب"
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>
                          <Clock size={16} className="me-1" />
                          مدة الدورة
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="duration"
                          value={formData.duration}
                          onChange={handleInputChange}
                          placeholder="مثال: 10 ساعات"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {/* Course Details */}
              <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">
                    <Target size={20} className="me-2" />
                    تفاصيل الدورة
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>المستوى</Form.Label>
                        <Form.Select
                          name="level"
                          value={formData.level}
                          onChange={handleInputChange}
                        >
                          <option value="مبتدئ">مبتدئ</option>
                          <option value="متوسط">متوسط</option>
                          <option value="متقدم">متقدم</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>التصنيف</Form.Label>
                        <Form.Select
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                        >
                          <option value="البرمجة">البرمجة</option>
                          <option value="التصميم">التصميم</option>
                          <option value="الشبكات">الشبكات</option>
                          <option value="الأمن السيبراني">الأمن السيبراني</option>
                          <option value="قواعد البيانات">قواعد البيانات</option>
                          <option value="الذكاء الاصطناعي">الذكاء الاصطناعي</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>السعر (ريال)</Form.Label>
                        <Form.Control
                          type="number"
                          name="price"
                          value={formData.price}
                          onChange={handleInputChange}
                          min="0"
                          step="0.01"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* Tags */}
                  <Form.Group className="mb-3">
                    <Form.Label>
                      <Tag size={16} className="me-1" />
                      الكلمات المفتاحية
                    </Form.Label>
                    <div className="d-flex mb-2">
                      <Form.Control
                        type="text"
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        placeholder="أدخل كلمة مفتاحية"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button
                        type="button"
                        variant="outline-primary"
                        className="ms-2"
                        onClick={addTag}
                      >
                        <Plus size={16} />
                      </Button>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          bg="primary"
                          className="d-flex align-items-center"
                        >
                          {tag}
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 ms-1 text-white"
                            onClick={() => removeTag(tag)}
                          >
                            ×
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </Form.Group>
                </Card.Body>
              </Card>
            </Col>

            {/* Sidebar */}
            <Col lg={4}>
              <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">إجراءات</h5>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={loading}
                      onClick={(e) => {
                        console.log('Button clicked!');
                        handleSubmit(e);
                      }}
                    >
                      {loading ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" />
                          جاري الحفظ...
                        </>
                      ) : (
                        <>
                          <Save size={16} className="me-1" />
                          حفظ الدورة
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline-secondary"
                      onClick={() => router.back()}
                    >
                      إلغاء
                    </Button>
                  </div>
                </Card.Body>
              </Card>

              {/* Course Preview */}
              <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">معاينة الدورة</h5>
                </Card.Header>
                <Card.Body>
                  <div className="text-center mb-3">
                    <div className="bg-light rounded p-4 mb-3">
                      <Upload size={32} className="text-muted" />
                      <p className="small text-muted mb-0">صورة الدورة</p>
                    </div>
                  </div>
                  <h6 className="fw-bold">{formData.title || 'عنوان الدورة'}</h6>
                  <p className="text-muted small">
                    {formData.description || 'وصف الدورة سيظهر هنا...'}
                  </p>
                  <div className="d-flex justify-content-between align-items-center">
                    <Badge bg="primary">{formData.level}</Badge>
                    <small className="text-muted">{formData.instructor || 'المدرب'}</small>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Form>
      </Container>
    </div>
  );
}
