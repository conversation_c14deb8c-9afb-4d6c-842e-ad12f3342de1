'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../contexts/AuthContext';
import ProtectedRoute from '../../../components/ProtectedRoute';
import api from '../../../lib/api';
import { toast } from 'react-toastify';
import {
  BookOpen,
  Save,
  X,
  Upload,
  Eye,
  Star,
  FileText,
  Globe
} from 'lucide-react';

export default function AddCourse() {
  const { user } = useAuth();
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    instructor: '',
    category: '',
    level: 'beginner',
    duration: '',
    price: 0,
    image: '',
    isActive: true,
    isFeatured: false,
    prerequisites: '',
    learningOutcomes: '',
    language: 'ar',
    tags: ''
  });

  const categories = [
    'البرمجة',
    'التصميم',
    'التسويق الرقمي',
    'إدارة الأعمال',
    'الذكاء الاصطناعي',
    'الأمن السيبراني',
    'تطوير المواقع',
    'تطوير التطبيقات',
    'قواعد البيانات',
    'الشبكات',
    'أخرى'
  ];

  const levels = [
    { value: 'beginner', label: 'مبتدئ' },
    { value: 'intermediate', label: 'متوسط' },
    { value: 'advanced', label: 'متقدم' }
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('العنوان والوصف مطلوبان');
      return;
    }

    try {
      setLoading(true);

      // تحضير البيانات للإرسال
      const courseData = {
        ...formData,
        instructor: formData.instructor || user.name,
        price: parseFloat(formData.price) || 0,
        duration: parseInt(formData.duration) || 0,
        prerequisites: formData.prerequisites.split('\n').filter(p => p.trim()),
        learningOutcomes: formData.learningOutcomes.split('\n').filter(o => o.trim()),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      const response = await api.post('/courses', courseData);

      toast.success('تم إنشاء الدورة بنجاح!');
      router.push(`/admin/courses/${response.data.course._id}/content`);

    } catch (error) {
      console.error('Error creating course:', error);
      const errorMessage = error.response?.data?.message || 'خطأ في إنشاء الدورة';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h2 className="mb-1">إضافة دورة جديدة</h2>
                <p className="text-muted mb-0">
                  أنشئ دورة تدريبية جديدة وأضف المحتوى التعليمي
                </p>
              </div>
              <button
                type="button"
                className="btn btn-outline-secondary"
                onClick={() => router.back()}
              >
                <X className="w-4 h-4 me-1" />
                إلغاء
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="row">
            {/* Main Form */}
            <div className="col-lg-8">
              {/* Basic Information */}
              <div className="card mb-4">
                <div className="card-header">
                  <h5 className="mb-0">
                    <BookOpen className="w-5 h-5 me-2" />
                    المعلومات الأساسية
                  </h5>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-12 mb-3">
                      <label className="form-label">عنوان الدورة *</label>
                      <input
                        type="text"
                        className="form-control"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="أدخل عنوان الدورة"
                        required
                      />
                    </div>

                    <div className="col-12 mb-3">
                      <label className="form-label">وصف الدورة *</label>
                      <textarea
                        className="form-control"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows="4"
                        placeholder="وصف مفصل للدورة وما ستتعلمه"
                        required
                      ></textarea>
                    </div>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>
                          <User size={16} className="me-1" />
                          اسم المدرب *
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="instructor"
                          value={formData.instructor}
                          onChange={handleInputChange}
                          placeholder="أدخل اسم المدرب"
                          required
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>
                          <Clock size={16} className="me-1" />
                          مدة الدورة
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="duration"
                          value={formData.duration}
                          onChange={handleInputChange}
                          placeholder="مثال: 10 ساعات"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {/* Course Details */}
              <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">
                    <Target size={20} className="me-2" />
                    تفاصيل الدورة
                  </h5>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>المستوى</Form.Label>
                        <Form.Select
                          name="level"
                          value={formData.level}
                          onChange={handleInputChange}
                        >
                          <option value="مبتدئ">مبتدئ</option>
                          <option value="متوسط">متوسط</option>
                          <option value="متقدم">متقدم</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>التصنيف</Form.Label>
                        <Form.Select
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                        >
                          <option value="البرمجة">البرمجة</option>
                          <option value="التصميم">التصميم</option>
                          <option value="الشبكات">الشبكات</option>
                          <option value="الأمن السيبراني">الأمن السيبراني</option>
                          <option value="قواعد البيانات">قواعد البيانات</option>
                          <option value="الذكاء الاصطناعي">الذكاء الاصطناعي</option>
                        </Form.Select>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>السعر (ريال)</Form.Label>
                        <Form.Control
                          type="number"
                          name="price"
                          value={formData.price}
                          onChange={handleInputChange}
                          min="0"
                          step="0.01"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* Tags */}
                  <Form.Group className="mb-3">
                    <Form.Label>
                      <Tag size={16} className="me-1" />
                      الكلمات المفتاحية
                    </Form.Label>
                    <div className="d-flex mb-2">
                      <Form.Control
                        type="text"
                        value={currentTag}
                        onChange={(e) => setCurrentTag(e.target.value)}
                        placeholder="أدخل كلمة مفتاحية"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button
                        type="button"
                        variant="outline-primary"
                        className="ms-2"
                        onClick={addTag}
                      >
                        <Plus size={16} />
                      </Button>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          bg="primary"
                          className="d-flex align-items-center"
                        >
                          {tag}
                          <Button
                            variant="link"
                            size="sm"
                            className="p-0 ms-1 text-white"
                            onClick={() => removeTag(tag)}
                          >
                            ×
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </Form.Group>
                </Card.Body>
              </Card>
            </Col>

            {/* Sidebar */}
            <Col lg={4}>
              <Card className={`border-0 shadow-sm mb-4 ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">إجراءات</h5>
                </Card.Header>
                <Card.Body>
                  <div className="d-grid gap-2">
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={loading}
                      onClick={(e) => {
                        console.log('Button clicked!');
                        handleSubmit(e);
                      }}
                    >
                      {loading ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" />
                          جاري الحفظ...
                        </>
                      ) : (
                        <>
                          <Save size={16} className="me-1" />
                          حفظ الدورة
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline-secondary"
                      onClick={() => router.back()}
                    >
                      إلغاء
                    </Button>
                  </div>
                </Card.Body>
              </Card>

              {/* Course Preview */}
              <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
                <Card.Header className="bg-transparent border-0">
                  <h5 className="mb-0">معاينة الدورة</h5>
                </Card.Header>
                <Card.Body>
                  <div className="text-center mb-3">
                    <div className="bg-light rounded p-4 mb-3">
                      <Upload size={32} className="text-muted" />
                      <p className="small text-muted mb-0">صورة الدورة</p>
                    </div>
                  </div>
                  <h6 className="fw-bold">{formData.title || 'عنوان الدورة'}</h6>
                  <p className="text-muted small">
                    {formData.description || 'وصف الدورة سيظهر هنا...'}
                  </p>
                  <div className="d-flex justify-content-between align-items-center">
                    <Badge bg="primary">{formData.level}</Badge>
                    <small className="text-muted">{formData.instructor || 'المدرب'}</small>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Form>
      </Container>
    </div>
  );
}
