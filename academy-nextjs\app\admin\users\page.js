'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Button, Form, Modal, InputGroup } from 'react-bootstrap';
import { useAuth } from '../../../contexts/AuthContext';
import { useTheme } from '../../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { 
  Users, 
  Search, 
  Filter, 
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Plus,
  Download,
  Mail,
  Calendar
} from 'lucide-react';
import { toast } from 'react-toastify';

export default function AdminUsers() {
  const { user, isAdminOrSuperAdmin, api } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [modalType, setModalType] = useState('view'); // view, edit, delete

  useEffect(() => {
    if (!isAdminOrSuperAdmin) {
      router.push('/unauthorized');
      return;
    }
    
    fetchUsers();
  }, [isAdminOrSuperAdmin, router]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      
      // محاكاة بيانات المستخدمين
      const mockUsers = [
        {
          _id: '1',
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          role: 'student',
          isActive: true,
          createdAt: new Date('2024-01-15'),
          lastActive: new Date(),
          loginCount: 45,
          completedCourses: 3,
          totalPoints: 850
        },
        {
          _id: '2',
          name: 'فاطمة سالم',
          email: '<EMAIL>',
          role: 'student',
          isActive: true,
          createdAt: new Date('2024-02-10'),
          lastActive: new Date(Date.now() - 86400000),
          loginCount: 32,
          completedCourses: 2,
          totalPoints: 650
        },
        {
          _id: '3',
          name: 'محمد الإداري',
          email: '<EMAIL>',
          role: 'admin',
          isActive: true,
          createdAt: new Date('2023-12-01'),
          lastActive: new Date(Date.now() - 3600000),
          loginCount: 120,
          completedCourses: 0,
          totalPoints: 0
        },
        {
          _id: '4',
          name: 'سارة أحمد',
          email: '<EMAIL>',
          role: 'student',
          isActive: false,
          createdAt: new Date('2024-03-05'),
          lastActive: new Date(Date.now() - 604800000),
          loginCount: 8,
          completedCourses: 0,
          totalPoints: 120
        },
        {
          _id: '5',
          name: 'علي المدير العام',
          email: '<EMAIL>',
          role: 'super-admin',
          isActive: true,
          createdAt: new Date('2023-10-01'),
          lastActive: new Date(Date.now() - 1800000),
          loginCount: 200,
          completedCourses: 0,
          totalPoints: 0
        }
      ];

      setUsers(mockUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('فشل في تحميل بيانات المستخدمين');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = !roleFilter || user.role === roleFilter;
    const matchesStatus = !statusFilter || 
                         (statusFilter === 'active' && user.isActive) ||
                         (statusFilter === 'inactive' && !user.isActive);
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleBadge = (role) => {
    switch (role) {
      case 'super-admin':
        return <Badge bg="danger">مدير عام</Badge>;
      case 'admin':
        return <Badge bg="warning">مدير</Badge>;
      case 'student':
        return <Badge bg="primary">طالب</Badge>;
      default:
        return <Badge bg="secondary">{role}</Badge>;
    }
  };

  const getStatusBadge = (isActive) => {
    return isActive ? 
      <Badge bg="success">نشط</Badge> : 
      <Badge bg="secondary">غير نشط</Badge>;
  };

  const handleUserAction = (user, action) => {
    setSelectedUser(user);
    setModalType(action);
    setShowModal(true);
  };

  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      // محاكاة تغيير حالة المستخدم
      setUsers(prev => 
        prev.map(user => 
          user._id === userId 
            ? { ...user, isActive: !currentStatus }
            : user
        )
      );
      toast.success('تم تحديث حالة المستخدم بنجاح');
    } catch (error) {
      toast.error('فشل في تحديث حالة المستخدم');
    }
  };

  const deleteUser = async (userId) => {
    try {
      // محاكاة حذف المستخدم
      setUsers(prev => prev.filter(user => user._id !== userId));
      toast.success('تم حذف المستخدم بنجاح');
      setShowModal(false);
    } catch (error) {
      toast.error('فشل في حذف المستخدم');
    }
  };

  const formatLastActive = (date) => {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    return `منذ ${days} يوم`;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="display-6 fw-bold mb-2">إدارة المستخدمين</h1>
                <p className="text-muted">إدارة حسابات المستخدمين وصلاحياتهم</p>
              </div>
              <div className="d-flex gap-2">
                <Button variant="success">
                  <Plus size={16} className="me-1" />
                  إضافة مستخدم
                </Button>
                <Button variant="outline-secondary">
                  <Download size={16} className="me-1" />
                  تصدير
                </Button>
              </div>
            </div>
          </Col>
        </Row>

        {/* Filters */}
        <Row className="mb-4">
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Row className="g-3">
                  <Col md={4}>
                    <InputGroup>
                      <InputGroup.Text>
                        <Search size={16} />
                      </InputGroup.Text>
                      <Form.Control
                        type="text"
                        placeholder="البحث بالاسم أو البريد الإلكتروني..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </InputGroup>
                  </Col>
                  <Col md={3}>
                    <Form.Select
                      value={roleFilter}
                      onChange={(e) => setRoleFilter(e.target.value)}
                    >
                      <option value="">جميع الأدوار</option>
                      <option value="student">طالب</option>
                      <option value="admin">مدير</option>
                      <option value="super-admin">مدير عام</option>
                    </Form.Select>
                  </Col>
                  <Col md={3}>
                    <Form.Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <option value="">جميع الحالات</option>
                      <option value="active">نشط</option>
                      <option value="inactive">غير نشط</option>
                    </Form.Select>
                  </Col>
                  <Col md={2}>
                    <Button 
                      variant="outline-secondary" 
                      className="w-100"
                      onClick={() => {
                        setSearchTerm('');
                        setRoleFilter('');
                        setStatusFilter('');
                      }}
                    >
                      <Filter size={16} />
                    </Button>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Users Table */}
        <Row>
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <Users size={20} className="me-2" />
                  المستخدمين ({filteredUsers.length})
                </h5>
              </Card.Header>
              <Card.Body className="p-0">
                <div className="table-responsive">
                  <Table hover className={`mb-0 ${isDark ? 'table-dark' : ''}`}>
                    <thead className="table-light">
                      <tr>
                        <th>المستخدم</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ التسجيل</th>
                        <th>آخر نشاط</th>
                        <th>الإحصائيات</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredUsers.map((user) => (
                        <tr key={user._id}>
                          <td>
                            <div className="d-flex align-items-center">
                              <div className="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                   style={{ width: '40px', height: '40px' }}>
                                <span className="text-white small fw-bold">
                                  {user.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div>
                                <div className="fw-bold">{user.name}</div>
                                <small className="text-muted">
                                  <Mail size={12} className="me-1" />
                                  {user.email}
                                </small>
                              </div>
                            </div>
                          </td>
                          <td>{getRoleBadge(user.role)}</td>
                          <td>{getStatusBadge(user.isActive)}</td>
                          <td>
                            <small>
                              <Calendar size={12} className="me-1" />
                              {user.createdAt.toLocaleDateString('ar-SA')}
                            </small>
                          </td>
                          <td>
                            <small className="text-muted">
                              {formatLastActive(user.lastActive)}
                            </small>
                          </td>
                          <td>
                            <div className="small">
                              <div>دورات: {user.completedCourses}</div>
                              <div>نقاط: {user.totalPoints}</div>
                              <div>دخول: {user.loginCount}</div>
                            </div>
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <Button
                                variant="outline-info"
                                size="sm"
                                onClick={() => handleUserAction(user, 'view')}
                              >
                                <Eye size={14} />
                              </Button>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => handleUserAction(user, 'edit')}
                              >
                                <Edit size={14} />
                              </Button>
                              <Button
                                variant={user.isActive ? "outline-warning" : "outline-success"}
                                size="sm"
                                onClick={() => toggleUserStatus(user._id, user.isActive)}
                              >
                                {user.isActive ? <UserX size={14} /> : <UserCheck size={14} />}
                              </Button>
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleUserAction(user, 'delete')}
                              >
                                <Trash2 size={14} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* User Modal */}
        <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
          <Modal.Header closeButton>
            <Modal.Title>
              {modalType === 'view' && 'عرض المستخدم'}
              {modalType === 'edit' && 'تعديل المستخدم'}
              {modalType === 'delete' && 'حذف المستخدم'}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {selectedUser && (
              <>
                {modalType === 'view' && (
                  <div>
                    <Row>
                      <Col md={6}>
                        <p><strong>الاسم:</strong> {selectedUser.name}</p>
                        <p><strong>البريد الإلكتروني:</strong> {selectedUser.email}</p>
                        <p><strong>الدور:</strong> {getRoleBadge(selectedUser.role)}</p>
                        <p><strong>الحالة:</strong> {getStatusBadge(selectedUser.isActive)}</p>
                      </Col>
                      <Col md={6}>
                        <p><strong>تاريخ التسجيل:</strong> {selectedUser.createdAt.toLocaleDateString('ar-SA')}</p>
                        <p><strong>آخر نشاط:</strong> {formatLastActive(selectedUser.lastActive)}</p>
                        <p><strong>عدد مرات الدخول:</strong> {selectedUser.loginCount}</p>
                        <p><strong>الدورات المكتملة:</strong> {selectedUser.completedCourses}</p>
                      </Col>
                    </Row>
                  </div>
                )}
                
                {modalType === 'delete' && (
                  <div className="text-center">
                    <div className="text-danger mb-3">
                      <Trash2 size={48} />
                    </div>
                    <h5>هل أنت متأكد من حذف هذا المستخدم؟</h5>
                    <p className="text-muted">
                      سيتم حذف <strong>{selectedUser.name}</strong> نهائياً ولا يمكن التراجع عن هذا الإجراء.
                    </p>
                  </div>
                )}
              </>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowModal(false)}>
              إلغاء
            </Button>
            {modalType === 'delete' && (
              <Button 
                variant="danger" 
                onClick={() => deleteUser(selectedUser._id)}
              >
                حذف المستخدم
              </Button>
            )}
          </Modal.Footer>
        </Modal>
      </Container>
    </div>
  );
}
