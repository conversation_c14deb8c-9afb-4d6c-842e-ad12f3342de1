"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/courses/route";
exports.ids = ["app/api/courses/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_courses_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/courses/route.js */ \"(rsc)/./app/api/courses/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/courses/route\",\n        pathname: \"/api/courses\",\n        filename: \"route\",\n        bundlePath: \"app/api/courses/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\api\\\\courses\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Abdulmnoum_Downloads_project_bolt_academy_project_academy_nextjs_app_api_courses_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/courses/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/courses/route.js":
/*!**********************************!*\
  !*** ./app/api/courses/route.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/mongodb */ \"(rsc)/./lib/mongodb.js\");\n/* harmony import */ var _models_Course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../models/Course */ \"(rsc)/./models/Course.js\");\n\n\n\n\n// GET - جلب جميع الدورات (عام)\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\")) || 1;\n        const limit = parseInt(searchParams.get(\"limit\")) || 10;\n        const search = searchParams.get(\"search\") || \"\";\n        const level = searchParams.get(\"level\") || \"\";\n        const category = searchParams.get(\"category\") || \"\";\n        // بناء الاستعلام\n        const query = {\n            isActive: true\n        };\n        // البحث النصي\n        if (search) {\n            query.$text = {\n                $search: search\n            };\n        }\n        // فلترة حسب المستوى\n        if (level) {\n            query.level = level;\n        }\n        // فلترة حسب التصنيف\n        if (category) {\n            query.category = category;\n        }\n        // حساب التخطي\n        const skip = (page - 1) * limit;\n        // جلب الدورات\n        const courses = await _models_Course__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find(query).sort({\n            createdAt: -1\n        }).skip(skip).limit(limit).populate(\"createdBy\", \"name\").select(\"-units\"); // إخفاء المحتوى التفصيلي\n        // عدد الدورات الإجمالي\n        const total = await _models_Course__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments(query);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            courses,\n            pagination: {\n                current: page,\n                total: Math.ceil(total / limit),\n                count: courses.length,\n                totalCourses: total\n            }\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Courses fetch error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في جلب الدورات\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إنشاء دورة جديدة (للمدراء فقط)\nconst POST = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.withAuth)(async (request)=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const data = await request.json();\n        const { title, description, instructor, duration, level, category, tags, price, units } = data;\n        // التحقق من البيانات المطلوبة\n        if (!title || !description || !instructor) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"العنوان والوصف واسم المدرب مطلوبة\"\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء الدورة\n        const course = new _models_Course__WEBPACK_IMPORTED_MODULE_3__[\"default\"]({\n            title: title.trim(),\n            description: description.trim(),\n            instructor: instructor.trim(),\n            duration: duration || \"0 دقيقة\",\n            level: level || \"مبتدئ\",\n            category: category || \"عام\",\n            tags: tags || [],\n            price: price || 0,\n            units: units || [],\n            createdBy: request.user._id\n        });\n        await course.save();\n        // جلب الدورة مع بيانات المنشئ\n        const populatedCourse = await _models_Course__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(course._id).populate(\"createdBy\", \"name\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"تم إنشاء الدورة بنجاح\",\n            course: populatedCourse\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Course creation error:\", error);\n        if (error.name === \"ValidationError\") {\n            const messages = Object.values(error.errors).map((err)=>err.message);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: messages.join(\", \")\n            }, {\n                status: 400\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"خطأ في إنشاء الدورة\"\n        }, {\n            status: 500\n        });\n    }\n}, {\n    roles: [\n        \"admin\",\n        \"super-admin\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/courses/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   checkRole: () => (/* binding */ checkRole),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   roleCheckers: () => (/* binding */ roleCheckers),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./lib/mongodb.js\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/User */ \"(rsc)/./models/User.js\");\n\n\n\n\nconst JWT_SECRET = \"your-super-secret-jwt-key-here-make-it-long-and-complex\";\n// إنشاء JWT Token\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\n// التحقق من JWT Token\nfunction verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        return null;\n    }\n}\n// Middleware للتحقق من المصادقة\nasync function authenticateUser(request) {\n    try {\n        const token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return {\n                error: \"Token مطلوب\",\n                status: 401\n            };\n        }\n        const decoded = verifyToken(token);\n        if (!decoded) {\n            return {\n                error: \"Token غير صالح\",\n                status: 401\n            };\n        }\n        await (0,_mongodb__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(decoded.id).select(\"-password\");\n        if (!user || !user.isActive) {\n            return {\n                error: \"المستخدم غير موجود أو غير نشط\",\n                status: 401\n            };\n        }\n        return {\n            user,\n            status: 200\n        };\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            error: \"خطأ في المصادقة\",\n            status: 500\n        };\n    }\n}\n// التحقق من الصلاحيات\nfunction checkRole(user, allowedRoles) {\n    if (!user || !allowedRoles.includes(user.role)) {\n        return false;\n    }\n    return true;\n}\n// Middleware wrapper للـ API routes\nfunction withAuth(handler, options = {}) {\n    return async (request, context)=>{\n        const authResult = await authenticateUser(request);\n        if (authResult.error) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: authResult.error\n            }, {\n                status: authResult.status\n            });\n        }\n        // التحقق من الصلاحيات إذا كانت محددة\n        if (options.roles && !checkRole(authResult.user, options.roles)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                message: \"ليس لديك صلاحية للوصول لهذا المورد\"\n            }, {\n                status: 403\n            });\n        }\n        // إضافة المستخدم إلى السياق\n        request.user = authResult.user;\n        return handler(request, context);\n    };\n}\n// Helper functions للأدوار\nconst roleCheckers = {\n    isStudent: (user)=>user?.role === \"student\",\n    isAdmin: (user)=>user?.role === \"admin\",\n    isSuperAdmin: (user)=>user?.role === \"super-admin\",\n    isAdminOrSuperAdmin: (user)=>[\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.js":
/*!************************!*\
  !*** ./lib/mongodb.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = \"mongodb+srv://admin:<EMAIL>/academy?retryWrites=true&w=majority\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable inside .env.local\");\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            console.log(\"✅ Connected to MongoDB\");\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.js\n");

/***/ }),

/***/ "(rsc)/./models/Course.js":
/*!**************************!*\
  !*** ./models/Course.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\n// مخطط الدرس\nconst lessonSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    type: {\n        type: String,\n        enum: [\n            \"video\",\n            \"reading\",\n            \"exercise\"\n        ],\n        required: true\n    },\n    content: String,\n    videoUrl: String,\n    textContent: String,\n    duration: String,\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    _id: true\n});\n// مخطط السؤال للاختبار\nconst questionSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    question: {\n        type: String,\n        required: true\n    },\n    options: [\n        {\n            type: String,\n            required: true\n        }\n    ],\n    correctAnswer: {\n        type: String,\n        required: true\n    },\n    explanation: String\n}, {\n    _id: true\n});\n// مخطط الاختبار\nconst quizSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        default: \"اختبار الوحدة\"\n    },\n    questions: [\n        questionSchema\n    ],\n    passingScore: {\n        type: Number,\n        default: 70\n    },\n    timeLimit: {\n        type: Number,\n        default: 30\n    } // بالدقائق\n}, {\n    _id: false\n});\n// مخطط الوحدة\nconst unitSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: String,\n    lessons: [\n        lessonSchema\n    ],\n    quiz: quizSchema,\n    order: {\n        type: Number,\n        default: 0\n    }\n}, {\n    _id: true\n});\nconst courseSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: [\n            true,\n            \"عنوان الدورة مطلوب\"\n        ],\n        trim: true\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            \"وصف الدورة مطلوب\"\n        ]\n    },\n    instructor: {\n        type: String,\n        required: [\n            true,\n            \"اسم المدرب مطلوب\"\n        ],\n        default: \"مدرب المنصة\"\n    },\n    duration: {\n        type: String,\n        default: \"0 دقيقة\"\n    },\n    level: {\n        type: String,\n        enum: [\n            \"مبتدئ\",\n            \"متوسط\",\n            \"متقدم\",\n            \"Beginner\",\n            \"Intermediate\",\n            \"Advanced\"\n        ],\n        default: \"مبتدئ\"\n    },\n    category: {\n        type: String,\n        default: \"عام\"\n    },\n    tags: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    image: String,\n    video: String,\n    price: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    isFeatured: {\n        type: Boolean,\n        default: false\n    },\n    // إحصائيات\n    enrollmentCount: {\n        type: Number,\n        default: 0\n    },\n    views: {\n        type: Number,\n        default: 0\n    },\n    rating: {\n        type: Number,\n        default: 0,\n        min: 0,\n        max: 5\n    },\n    ratingCount: {\n        type: Number,\n        default: 0\n    },\n    completionRate: {\n        type: Number,\n        default: 0\n    },\n    // المحتوى\n    units: [\n        unitSchema\n    ],\n    // المنشئ والمحرر\n    createdBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\",\n        required: true\n    },\n    updatedBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"User\"\n    }\n}, {\n    timestamps: true\n});\n// إنشاء فهرس للبحث النصي\ncourseSchema.index({\n    title: \"text\",\n    description: \"text\",\n    tags: \"text\",\n    instructor: \"text\"\n});\n// فهارس للأداء\ncourseSchema.index({\n    isActive: 1,\n    createdAt: -1\n});\ncourseSchema.index({\n    level: 1,\n    isActive: 1\n});\ncourseSchema.index({\n    category: 1,\n    isActive: 1\n});\ncourseSchema.index({\n    rating: -1,\n    isActive: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Course || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Course\", courseSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/Course.js\n");

/***/ }),

/***/ "(rsc)/./models/User.js":
/*!************************!*\
  !*** ./models/User.js ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: [\n            true,\n            \"الاسم مطلوب\"\n        ],\n        trim: true\n    },\n    email: {\n        type: String,\n        required: [\n            true,\n            \"البريد الإلكتروني مطلوب\"\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            \"كلمة المرور مطلوبة\"\n        ],\n        minlength: [\n            6,\n            \"كلمة المرور يجب أن تكون 6 أحرف على الأقل\"\n        ]\n    },\n    role: {\n        type: String,\n        enum: [\n            \"student\",\n            \"admin\",\n            \"super-admin\"\n        ],\n        default: \"student\"\n    },\n    avatar: {\n        type: String,\n        default: null\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    lastActive: {\n        type: Date,\n        default: Date.now\n    },\n    loginCount: {\n        type: Number,\n        default: 0\n    },\n    // إحصائيات الطالب\n    completedCourses: [\n        {\n            courseId: {\n                type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n                ref: \"Course\"\n            },\n            completedAt: Date,\n            level: String,\n            score: Number\n        }\n    ],\n    totalPoints: {\n        type: Number,\n        default: 0\n    },\n    achievements: [\n        {\n            type: String\n        }\n    ],\n    // تفضيلات المستخدم\n    preferences: {\n        theme: {\n            type: String,\n            enum: [\n                \"light\",\n                \"dark\"\n            ],\n            default: \"light\"\n        },\n        language: {\n            type: String,\n            default: \"ar\"\n        },\n        notifications: {\n            type: Boolean,\n            default: true\n        }\n    }\n}, {\n    timestamps: true\n});\n// Hash password before saving\nuserSchema.pre(\"save\", async function(next) {\n    if (!this.isModified(\"password\")) return next();\n    try {\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Compare password method\nuserSchema.methods.comparePassword = async function(candidatePassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(candidatePassword, this.password);\n};\n// Remove password from JSON output\nuserSchema.methods.toJSON = function() {\n    const userObject = this.toObject();\n    delete userObject.password;\n    return userObject;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", userSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./models/User.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcourses%2Froute&page=%2Fapi%2Fcourses%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcourses%2Froute.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();