import axios from 'axios';

// تكوين API URL بطريقة آمنة
const getApiUrl = () => {
  // في بيئة التطوير
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return 'http://localhost:5000/api';
  }

  // في بيئة الإنتاج
  try {
    // محاولة استخدام import.meta.env (Vite)
    if (import.meta && import.meta.env && import.meta.env.VITE_API_URL) {
      return `${import.meta.env.VITE_API_URL}/api`;
    }
  } catch (e) {
    console.warn('Could not access import.meta.env:', e);
  }

  // fallback للإنتاج
  return 'https://academy-project-production.up.railway.app/api';
};

const API_URL = getApiUrl();

const api = axios.create({
  baseURL: API_URL,
});

// Add token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (userData) => api.post('/auth/login', userData),
  getProfile: () => api.get('/user/profile'), 
  updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
};

export const coursesAPI = {
  // الوظائف الأساسية
  getAll: (params) => api.get('/courses', { params }),
  getById: (id) => api.get(`/courses/${id}`),
  create: (formData) => api.post('/courses', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  update: (id, data) => {
    // إذا كانت البيانات FormData، استخدم multipart/form-data
    if (data instanceof FormData) {
      return api.put(`/courses/${id}`, data, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    }
    // وإلا استخدم JSON
    return api.put(`/courses/${id}`, data);
  },
  delete: (id) => api.delete(`/courses/${id}`),

  // إدارة حالة الدورة
  toggleStatus: (id) => api.patch(`/courses/${id}/toggle-status`),
  softDelete: (id) => api.patch(`/courses/${id}/soft-delete`),
  permanentDelete: (id) => api.delete(`/courses/${id}/permanent`),
  duplicate: (id) => api.post(`/courses/${id}/duplicate`),

  // البحث والإحصائيات
  advancedSearch: (params) => api.get('/courses/search/advanced', { params }),
  getStats: () => api.get('/courses/stats/overview'),

  // التسجيل في الدورات
  enroll: (id) => api.post(`/courses/${id}/enroll`),
  checkEnrollmentStatus: (id) => api.get(`/courses/${id}/enrollment-status`),

  // إدارة الدروس
  updateLesson: (courseId, unitIndex, lessonIndex, lessonData) =>
    api.put(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`, lessonData),
  addLesson: (courseId, unitIndex, lessonData) =>
    api.post(`/courses/${courseId}/units/${unitIndex}/lessons`, lessonData),
  deleteLesson: (courseId, unitIndex, lessonIndex) =>
    api.delete(`/courses/${courseId}/units/${unitIndex}/lessons/${lessonIndex}`),
};
export const userAPI = {
  getUsers: () => api.get('/user/users'),

  getProfile: () => api.get('/user/profile'),
  // updateProfile: (id, data) => api.put(`/user/profile/${id}`, data),
  getCourses: () => api.get('/user/courses'),

}

export const adminAPI = {
  getUsers: () => api.get('/admin/users'),
  // updateUserRole: (id, role) => api.put(`/admin/users/${id}/role`, { role }),
  // deleteUser: (id) => api.delete(`/admin/users/${id}`),
  getCourses: () => api.get('/admin/courses'),
  getStatistics: () => api.get('/admin/statistics'),
};
// Super Admin API - صلاحيات كاملة للمدير العام
export const supedAdminAPI = {
  // Users management
  getUsers: () => api.get('/super-admin/users'),
  updateUserRole: (userId, role) => api.put(`/super-admin/users/${userId}/role`, { role }),
  deleteUser: (userId) => api.delete(`/super-admin/users/${userId}`),
  createUser: (userData) => api.post('/super-admin/users', userData),

  // System management
  getSystemStats: () => api.get('/super-admin/system/stats'),
  getSystemLogs: () => api.get('/super-admin/system/logs'),
  getStatistics: () => api.get('/super-admin/statistics'),
  getSystemSettings: () => api.get('/super-admin/system/settings'),
  updateSystemSettings: (settings) => api.put('/super-admin/system/settings', settings),
  toggleMaintenanceMode: (enabled) => api.put('/super-admin/system/maintenance', { enabled }),

  // Advanced analytics
  getAdvancedAnalytics: () => api.get('/super-admin/analytics'),
  getDetailedReports: () => api.get('/super-admin/analytics/detailed'),
  exportAnalytics: (format) => api.get(`/super-admin/analytics/export?format=${format}`),

  // Permissions management
  getAllPermissions: () => api.get('/super-admin/permissions'),
  updatePermissions: (userId, permissions) => api.put(`/super-admin/permissions/${userId}`, { permissions }),

  // Course management (full control)
  getAllCourses: () => api.get('/super-admin/courses'),
  forceDeleteCourse: (courseId) => api.delete(`/super-admin/courses/${courseId}/force`),
  toggleCourseStatus: (courseId, status) => api.put(`/super-admin/courses/${courseId}/status`, { isActive: status }),
  getCourseAnalytics: (courseId) => api.get(`/super-admin/courses/${courseId}/analytics`),
  bulkUpdateCourses: (updates) => api.put('/super-admin/courses/bulk', updates),

  // Backup and maintenance
  createBackup: () => api.post('/super-admin/system/backup'),
  restoreBackup: (backupId) => api.post(`/super-admin/system/backup/${backupId}/restore`),
  getBackupHistory: () => api.get('/super-admin/system/backup/history'),

  // Logs and monitoring
  getSystemLogs: (params) => api.get('/super-admin/system/logs', { params }),
  getErrorLogs: () => api.get('/super-admin/system/logs/errors'),
  getAccessLogs: () => api.get('/super-admin/system/logs/access'),
  clearLogs: (type) => api.delete(`/super-admin/system/logs/${type}`)
};

export const chatbotAPI = {
  sendMessage: (message) => api.post('/chatbot', { message }),
};

export default api;