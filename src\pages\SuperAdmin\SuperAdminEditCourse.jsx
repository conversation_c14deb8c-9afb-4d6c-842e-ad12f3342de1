import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Button, 
  Alert, 
  Row, 
  Col,
  Spinner,
  Badge,
  Modal
} from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { coursesAPI } from '../../services/api';
import { API_CONFIG } from '../../config/api';
import { 
  BookOpen, 
  Save, 
  ArrowLeft,
  Upload,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Edit,
  Eye
} from 'lucide-react';

const SuperAdminEditCourse = () => {
  const { id } = useParams();
  const { isDark } = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    duration: '',
    level: 'beginner',
    category: '',
    price: 0,
    isActive: true,
    image: '',
    video: '',
    units: []
  });

  const [imageFile, setImageFile] = useState(null);
  const [videoFile, setVideoFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');

  useEffect(() => {
    fetchCourse();
  }, [id]);

  const fetchCourse = async () => {
    try {
      setLoading(true);
      const response = await coursesAPI.getById(id);
      console.log('Course response:', response); // للتشخيص

      // التحقق من بنية البيانات
      let courseData;
      if (response && response.data) {
        courseData = response.data;
      } else if (response) {
        courseData = response;
      } else {
        throw new Error('No course data received');
      }

      // تأكد من وجود البيانات المطلوبة
      const processedCourseData = {
        title: courseData.title || '',
        description: courseData.description || '',
        duration: courseData.duration || '',
        level: courseData.level || 'beginner',
        category: courseData.category || '',
        price: courseData.price || 0,
        isActive: courseData.isActive !== undefined ? courseData.isActive : true,
        image: courseData.image || '',
        video: courseData.video || '',
        units: Array.isArray(courseData.units) ? courseData.units : []
      };

      setCourseData(processedCourseData);

      // Set image preview if exists
      if (processedCourseData.image) {
        const imageUrl = processedCourseData.image.startsWith('http')
          ? processedCourseData.image
          : `${API_CONFIG.BASE_URL}/${processedCourseData.image}`;
        setImagePreview(imageUrl);
      }
    } catch (err) {
      console.error('Error fetching course:', err);

      // في حالة الخطأ، استخدم بيانات وهمية للاختبار
      const mockCourse = {
        title: `دورة تجريبية ${id}`,
        description: 'هذه دورة تجريبية للاختبار. يمكنك تعديل جميع البيانات هنا.',
        duration: '6 أسابيع',
        level: 'intermediate',
        category: 'تجريبي',
        price: 199,
        isActive: true,
        image: '',
        video: '',
        units: [
          {
            title: 'الوحدة الأولى',
            description: 'مقدمة في الموضوع',
            lessons: [
              {
                title: 'الدرس الأول',
                content: 'محتوى الدرس الأول',
                videoUrl: '',
                duration: '15 دقيقة'
              }
            ],
            quiz: {
              questions: [
                {
                  question: 'ما هو الهدف من هذه الدورة؟',
                  options: ['التعلم', 'التطوير', 'الممارسة', 'جميع ما سبق'],
                  correctAnswer: 3
                }
              ]
            }
          }
        ]
      };

      setCourseData(mockCourse);
      setError('تم تحميل بيانات تجريبية - ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCourseData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (type === 'image') {
      setImageFile(file);
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => setImagePreview(e.target.result);
        reader.readAsDataURL(file);
      }
    } else if (type === 'video') {
      setVideoFile(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      
      // Add course data
      Object.keys(courseData).forEach(key => {
        if (key === 'units') {
          formData.append(key, JSON.stringify(courseData[key]));
        } else if (key !== 'image' && key !== 'video') {
          formData.append(key, courseData[key]);
        }
      });

      // Add files if selected
      if (imageFile) {
        formData.append('image', imageFile);
      }
      if (videoFile) {
        formData.append('video', videoFile);
      }

      await coursesAPI.update(id, formData);
      setSuccess('تم تحديث الدورة بنجاح!');
      
      setTimeout(() => {
        navigate('/super-admin/courses');
      }, 2000);

    } catch (err) {
      setError('فشل في تحديث الدورة: ' + (err.response?.data?.message || err.message));
      console.error('Error updating course:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    try {
      await coursesAPI.delete(id);
      setSuccess('تم حذف الدورة بنجاح!');
      setTimeout(() => {
        navigate('/super-admin/courses');
      }, 1500);
    } catch (err) {
      setError('فشل في حذف الدورة');
      console.error('Error deleting course:', err);
    }
    setShowDeleteModal(false);
  };

  const toggleStatus = async () => {
    try {
      const newStatus = !courseData.isActive;
      await coursesAPI.update(id, { isActive: newStatus });
      setCourseData(prev => ({ ...prev, isActive: newStatus }));
      setSuccess(`تم ${newStatus ? 'تفعيل' : 'إيقاف'} الدورة بنجاح!`);
    } catch (err) {
      setError('فشل في تغيير حالة الدورة');
      console.error('Error toggling status:', err);
    }
  };

  // وظائف إدارة الوحدات والدروس
  const addUnit = () => {
    setCourseData(prev => ({
      ...prev,
      units: [
        ...prev.units,
        {
          title: '',
          description: '',
          lessons: [
            {
              title: '',
              content: '',
              videoUrl: '',
              duration: ''
            }
          ],
          quiz: {
            questions: [
              {
                question: '',
                options: ['', '', '', ''],
                correctAnswer: 0
              }
            ]
          }
        }
      ]
    }));
  };

  const removeUnit = (unitIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.filter((_, index) => index !== unitIndex)
    }));
  };

  const updateUnit = (unitIndex, field, value) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) =>
        index === unitIndex ? { ...unit, [field]: value } : unit
      )
    }));
  };

  const addLesson = (unitIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) =>
        index === unitIndex
          ? {
              ...unit,
              lessons: [
                ...unit.lessons,
                {
                  title: '',
                  content: '',
                  videoUrl: '',
                  duration: ''
                }
              ]
            }
          : unit
      )
    }));
  };

  const removeLesson = (unitIndex, lessonIndex) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, index) =>
        index === unitIndex
          ? {
              ...unit,
              lessons: unit.lessons.filter((_, lIndex) => lIndex !== lessonIndex)
            }
          : unit
      )
    }));
  };

  const updateLesson = (unitIndex, lessonIndex, field, value) => {
    setCourseData(prev => ({
      ...prev,
      units: prev.units.map((unit, uIndex) =>
        uIndex === unitIndex
          ? {
              ...unit,
              lessons: unit.lessons.map((lesson, lIndex) =>
                lIndex === lessonIndex ? { ...lesson, [field]: value } : lesson
              )
            }
          : unit
      )
    }));
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">جاري تحميل بيانات الدورة...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="mb-1">تعديل الدورة: {courseData.title}</h4>
          <p className="text-muted mb-0">تعديل وإدارة الدورة بصلاحيات المدير العام</p>
        </div>
        <div className="d-flex gap-2">
          <Button 
            variant={courseData.isActive ? "success" : "secondary"}
            onClick={toggleStatus}
            className="d-flex align-items-center"
          >
            {courseData.isActive ? <CheckCircle size={18} className="me-2" /> : <AlertTriangle size={18} className="me-2" />}
            {courseData.isActive ? 'نشطة' : 'غير نشطة'}
          </Button>
          <Button 
            variant="outline-secondary" 
            onClick={() => navigate('/super-admin/courses')}
            className="d-flex align-items-center"
          >
            <ArrowLeft size={18} className="me-2" />
            العودة للدورات
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <AlertTriangle size={18} className="me-2" />
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <CheckCircle size={18} className="me-2" />
          {success}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        {/* Basic Information */}
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
          <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
            <h6 className="mb-0 d-flex align-items-center">
              <BookOpen size={18} className="me-2" />
              المعلومات الأساسية
            </h6>
            <Badge bg={courseData.isActive ? 'success' : 'secondary'}>
              {courseData.isActive ? 'نشطة' : 'غير نشطة'}
            </Badge>
          </Card.Header>
          <Card.Body>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>عنوان الدورة *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={courseData.title}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل عنوان الدورة"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>المدة الزمنية</Form.Label>
                  <Form.Control
                    type="text"
                    name="duration"
                    value={courseData.duration}
                    onChange={handleInputChange}
                    placeholder="مثال: 4 أسابيع"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>المستوى</Form.Label>
                  <Form.Select
                    name="level"
                    value={courseData.level}
                    onChange={handleInputChange}
                  >
                    <option value="beginner">مبتدئ</option>
                    <option value="intermediate">متوسط</option>
                    <option value="advanced">متقدم</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>التصنيف</Form.Label>
                  <Form.Control
                    type="text"
                    name="category"
                    value={courseData.category}
                    onChange={handleInputChange}
                    placeholder="مثال: البرمجة، التصميم"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>السعر</Form.Label>
                  <Form.Control
                    type="number"
                    name="price"
                    value={courseData.price}
                    onChange={handleInputChange}
                    min="0"
                    step="0.01"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="d-flex align-items-center pt-4">
                  <Form.Check
                    type="checkbox"
                    name="isActive"
                    checked={courseData.isActive}
                    onChange={handleInputChange}
                    label="تفعيل الدورة"
                  />
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group>
                  <Form.Label>وصف الدورة *</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="description"
                    value={courseData.description}
                    onChange={handleInputChange}
                    required
                    placeholder="أدخل وصف مفصل للدورة"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Media Files */}
        <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
          <Card.Header className="bg-transparent border-0">
            <h6 className="mb-0 d-flex align-items-center">
              <Upload size={18} className="me-2" />
              الملفات والوسائط
            </h6>
          </Card.Header>
          <Card.Body>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>صورة الدورة</Form.Label>
                  <Form.Control
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, 'image')}
                  />
                  <Form.Text className="text-muted">
                    يفضل أن تكون الصورة بحجم 800x400 بكسل
                  </Form.Text>
                  {imagePreview && (
                    <div className="mt-3">
                      <img 
                        src={imagePreview} 
                        alt="Preview" 
                        className="img-thumbnail"
                        style={{ maxHeight: '200px', maxWidth: '100%' }}
                      />
                    </div>
                  )}
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>فيديو تعريفي (اختياري)</Form.Label>
                  <Form.Control
                    type="file"
                    accept="video/*"
                    onChange={(e) => handleFileChange(e, 'video')}
                  />
                  <Form.Text className="text-muted">
                    فيديو قصير للتعريف بالدورة
                  </Form.Text>
                  {courseData.video && !videoFile && (
                    <div className="mt-3">
                      <Badge bg="info">يوجد فيديو محفوظ</Badge>
                    </div>
                  )}
                </Form.Group>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        {/* Units and Lessons */}
        {courseData.units && courseData.units.length > 0 && (
          <Card className={`${isDark ? 'bg-dark text-light' : 'bg-white'} border-0 shadow-sm mb-4`}>
            <Card.Header className="bg-transparent border-0 d-flex justify-content-between align-items-center">
              <h6 className="mb-0 d-flex align-items-center">
                <BookOpen size={18} className="me-2" />
                الوحدات والدروس ({courseData.units.length} وحدة)
              </h6>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={addUnit}
                className="d-flex align-items-center"
              >
                <Plus size={16} className="me-1" />
                إضافة وحدة
              </Button>
            </Card.Header>
            <Card.Body>
              {courseData.units.map((unit, unitIndex) => (
                <Card key={unitIndex} className={`mb-3 ${isDark ? 'bg-secondary' : 'bg-light'}`}>
                  <Card.Header className="bg-transparent d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">الوحدة {unitIndex + 1}: {unit.title || 'بدون عنوان'}</h6>
                    {courseData.units.length > 1 && (
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => removeUnit(unitIndex)}
                      >
                        <Trash2 size={14} />
                      </Button>
                    )}
                  </Card.Header>
                  <Card.Body>
                    <Row className="g-3 mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>عنوان الوحدة</Form.Label>
                          <Form.Control
                            type="text"
                            value={unit.title || ''}
                            onChange={(e) => updateUnit(unitIndex, 'title', e.target.value)}
                            placeholder="أدخل عنوان الوحدة"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>وصف الوحدة</Form.Label>
                          <Form.Control
                            type="text"
                            value={unit.description || ''}
                            onChange={(e) => updateUnit(unitIndex, 'description', e.target.value)}
                            placeholder="وصف مختصر للوحدة"
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Lessons */}
                    <div className="mb-3">
                      <div className="d-flex justify-content-between align-items-center mb-2">
                        <h6 className="mb-0">الدروس ({unit.lessons?.length || 0})</h6>
                        <Button
                          variant="outline-success"
                          size="sm"
                          onClick={() => addLesson(unitIndex)}
                          className="d-flex align-items-center"
                        >
                          <Plus size={14} className="me-1" />
                          إضافة درس
                        </Button>
                      </div>

                      {unit.lessons && unit.lessons.map((lesson, lessonIndex) => (
                        <Card key={lessonIndex} className={`mb-2 ${isDark ? 'bg-dark' : 'bg-white'} border`}>
                          <Card.Body className="p-3">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <h6 className="mb-0">الدرس {lessonIndex + 1}: {lesson.title || 'بدون عنوان'}</h6>
                              {unit.lessons.length > 1 && (
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => removeLesson(unitIndex, lessonIndex)}
                                >
                                  <Trash2 size={12} />
                                </Button>
                              )}
                            </div>
                            <Row className="g-2">
                              <Col md={6}>
                                <Form.Control
                                  type="text"
                                  value={lesson.title || ''}
                                  onChange={(e) => updateLesson(unitIndex, lessonIndex, 'title', e.target.value)}
                                  placeholder="عنوان الدرس"
                                  size="sm"
                                />
                              </Col>
                              <Col md={6}>
                                <Form.Control
                                  type="text"
                                  value={lesson.duration || ''}
                                  onChange={(e) => updateLesson(unitIndex, lessonIndex, 'duration', e.target.value)}
                                  placeholder="المدة (مثال: 15 دقيقة)"
                                  size="sm"
                                />
                              </Col>
                              <Col md={12}>
                                <Form.Control
                                  as="textarea"
                                  rows={2}
                                  value={lesson.content || ''}
                                  onChange={(e) => updateLesson(unitIndex, lessonIndex, 'content', e.target.value)}
                                  placeholder="محتوى الدرس"
                                  size="sm"
                                />
                              </Col>
                            </Row>
                          </Card.Body>
                        </Card>
                      ))}
                    </div>
                  </Card.Body>
                </Card>
              ))}
            </Card.Body>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="d-flex justify-content-between">
          <Button 
            variant="danger" 
            onClick={() => setShowDeleteModal(true)}
            className="d-flex align-items-center"
          >
            <Trash2 size={18} className="me-2" />
            حذف الدورة
          </Button>
          
          <div className="d-flex gap-3">
            <Button 
              variant="secondary" 
              onClick={() => navigate('/super-admin/courses')}
              disabled={saving}
            >
              إلغاء
            </Button>
            <Button 
              type="submit" 
              variant="primary" 
              disabled={saving}
              className="d-flex align-items-center"
            >
              {saving ? (
                <>
                  <Spinner size="sm" className="me-2" />
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save size={18} className="me-2" />
                  حفظ التغييرات
                </>
              )}
            </Button>
          </div>
        </div>
      </Form>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">تأكيد الحذف</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <AlertTriangle size={48} className="text-danger mb-3" />
            <p>هل أنت متأكد من حذف الدورة: <strong>{courseData.title}</strong>؟</p>
            <p className="text-muted">سيتم حذف جميع البيانات المرتبطة بهذه الدورة نهائياً.</p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            إلغاء
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            حذف الدورة
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default SuperAdminEditCourse;
