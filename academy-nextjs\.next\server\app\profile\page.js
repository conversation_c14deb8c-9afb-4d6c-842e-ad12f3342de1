/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.js */ \"(rsc)/./app/profile/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/profile/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(ssr)/./app/layout.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDbGF5b3V0LmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLz8wNTE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJkdWxtbm91bVxcXFxEb3dubG9hZHNcXFxccHJvamVjdC1ib2x0LWFjYWRlbXlcXFxccHJvamVjdFxcXFxhY2FkZW15LW5leHRqc1xcXFxhcHBcXFxcbGF5b3V0LmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cprofile%5Cpage.js&server=true!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cprofile%5Cpage.js&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.js */ \"(ssr)/./app/profile/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDcHJvZmlsZSU1Q3BhZ2UuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWNhZGVteS1uZXh0anMvP2U2ZDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBYmR1bG1ub3VtXFxcXERvd25sb2Fkc1xcXFxwcm9qZWN0LWJvbHQtYWNhZGVteVxcXFxwcm9qZWN0XFxcXGFjYWRlbXktbmV4dGpzXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cprofile%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"(ssr)/./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Navbar */ \"(ssr)/./components/Navbar.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-vh-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {\n                                position: \"top-right\",\n                                autoClose: 5000,\n                                hideProgressBar: false,\n                                newestOnTop: false,\n                                closeOnClick: true,\n                                rtl: true,\n                                pauseOnFocusLoss: true,\n                                draggable: true,\n                                pauseOnHover: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/layout.js\n");

/***/ }),

/***/ "(ssr)/./app/profile/page.js":
/*!*****************************!*\
  !*** ./app/profile/page.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Profile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/ProgressBar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Badge,Button,Card,Col,Container,Form,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Form.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,Camera,Clock,Edit3,Mail,Save,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Profile() {\n    const { user, updateProfile, isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        preferences: {\n            theme: \"light\",\n            language: \"ar\",\n            notifications: true\n        }\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        completedCourses: 0,\n        totalPoints: 0,\n        achievements: [],\n        currentStreak: 0,\n        totalHours: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        if (user) {\n            setFormData({\n                name: user.name || \"\",\n                email: user.email || \"\",\n                preferences: {\n                    theme: user.preferences?.theme || \"light\",\n                    language: user.preferences?.language || \"ar\",\n                    notifications: user.preferences?.notifications ?? true\n                }\n            });\n            // جلب إحصائيات المستخدم الحقيقية\n            try {\n                const enrollmentsResponse = api.get(\"/enrollments\");\n                const enrollments = enrollmentsResponse.data.enrollments || [];\n                const enrollmentStats = enrollmentsResponse.data.stats || {};\n                setStats({\n                    completedCourses: enrollmentStats.completed || 0,\n                    totalPoints: user.totalPoints || 0,\n                    achievements: user.achievements || [],\n                    currentStreak: user.stats?.currentStreak || 0,\n                    totalHours: enrollmentStats.totalStudyHours || 0\n                });\n            } catch (error) {\n                console.error(\"Error fetching user stats:\", error);\n                // استخدام القيم الافتراضية في حالة الخطأ\n                setStats({\n                    completedCourses: 0,\n                    totalPoints: 0,\n                    achievements: [],\n                    currentStreak: 0,\n                    totalHours: 0\n                });\n            }\n        }\n    }, [\n        user,\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        if (name.includes(\".\")) {\n            const [parent, child] = name.split(\".\");\n            setFormData((prev)=>({\n                    ...prev,\n                    [parent]: {\n                        ...prev[parent],\n                        [child]: type === \"checkbox\" ? checked : value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: type === \"checkbox\" ? checked : value\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const result = await updateProfile({\n                name: formData.name,\n                preferences: formData.preferences\n            });\n            if (result.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(result.message);\n                setEditing(false);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(result.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"حدث خطأ في تحديث البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"d-flex justify-content-center align-items-center min-vh-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner-border text-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-vh-100 ${isDark ? \"bg-dark text-light\" : \"bg-light\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"py-5\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: `border-0 shadow-sm ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"align-items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            md: 2,\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"position-relative d-inline-block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary rounded-circle d-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            size: 40,\n                                                            className: \"text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        variant: \"primary\",\n                                                        size: \"sm\",\n                                                        className: \"position-absolute bottom-0 end-0 rounded-circle p-1\",\n                                                        style: {\n                                                            width: \"30px\",\n                                                            height: \"30px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 14\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                lineNumber: 156,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            md: 8,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-1\",\n                                                    children: user.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        user.email\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"d-flex gap-2 flex-wrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            bg: \"primary\",\n                                                            children: user.role === \"student\" ? \"طالب\" : user.role === \"admin\" ? \"مدير\" : \"مدير عام\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            bg: \"success\",\n                                                            children: \"نشط\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            bg: \"info\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    size: 12,\n                                                                    className: \"me-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"انضم في \",\n                                                                new Date(user.createdAt).toLocaleDateString(\"ar-SA\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            md: 2,\n                                            className: \"text-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: editing ? \"success\" : \"outline-primary\",\n                                                onClick: ()=>setEditing(!editing),\n                                                disabled: loading,\n                                                children: [\n                                                    editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 34\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ms-1\",\n                                                        children: editing ? \"حفظ\" : \"تعديل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-primary mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"fw-bold\",\n                                            children: stats.completedCourses\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"دورة مكتملة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-warning mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"fw-bold\",\n                                            children: stats.totalPoints\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"نقطة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-success mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"fw-bold\",\n                                            children: stats.currentStreak\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"يوم متتالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-info mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"fw-bold\",\n                                            children: stats.totalHours\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"ساعة تعلم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: `border-0 shadow-sm ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Body, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    activeKey: activeTab,\n                                    onSelect: setActiveTab,\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            eventKey: \"overview\",\n                                            title: \"نظرة عامة\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        md: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"mb-3\",\n                                                                children: \"الإنجازات الأخيرة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            stats.achievements.length > 0 ? stats.achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"d-flex align-items-center mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_Camera_Clock_Edit3_Mail_Save_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            size: 20,\n                                                                            className: \"text-warning me-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                            lineNumber: 259,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: achievement\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 29\n                                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                variant: \"info\",\n                                                                children: \"لم تحصل على أي إنجازات بعد. ابدأ بإكمال دورة!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        md: 6,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"mb-3\",\n                                                                children: \"التقدم الحالي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"d-flex justify-content-between mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                children: \"التقدم الإجمالي\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                children: \"75%\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        now: 75,\n                                                                        variant: \"primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"d-flex justify-content-between mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                children: \"الهدف الشهري\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 280,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                children: \"3/5 دورات\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        now: 60,\n                                                                        variant: \"success\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                lineNumber: 253,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            eventKey: \"settings\",\n                                            title: \"الإعدادات\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                onSubmit: handleSubmit,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"mb-3\",\n                                                                        children: \"المعلومات الشخصية\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Group, {\n                                                                        className: \"mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Label, {\n                                                                                children: \"الاسم الكامل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Control, {\n                                                                                type: \"text\",\n                                                                                name: \"name\",\n                                                                                value: formData.name,\n                                                                                onChange: handleInputChange,\n                                                                                disabled: !editing\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 296,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Group, {\n                                                                        className: \"mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Label, {\n                                                                                children: \"البريد الإلكتروني\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Control, {\n                                                                                type: \"email\",\n                                                                                value: formData.email,\n                                                                                disabled: true,\n                                                                                className: \"bg-light\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Text, {\n                                                                                className: \"text-muted\",\n                                                                                children: \"لا يمكن تغيير البريد الإلكتروني\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                md: 6,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"mb-3\",\n                                                                        children: \"التفضيلات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Group, {\n                                                                        className: \"mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Label, {\n                                                                                children: \"المظهر\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Select, {\n                                                                                name: \"preferences.theme\",\n                                                                                value: formData.preferences.theme,\n                                                                                onChange: handleInputChange,\n                                                                                disabled: !editing,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"light\",\n                                                                                        children: \"فاتح\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                        lineNumber: 327,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"dark\",\n                                                                                        children: \"مظلم\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                        lineNumber: 328,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Group, {\n                                                                        className: \"mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Label, {\n                                                                                children: \"اللغة\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Select, {\n                                                                                name: \"preferences.language\",\n                                                                                value: formData.preferences.language,\n                                                                                onChange: handleInputChange,\n                                                                                disabled: !editing,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"ar\",\n                                                                                        children: \"العربية\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                        lineNumber: 339,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"en\",\n                                                                                        children: \"English\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                        lineNumber: 340,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Group, {\n                                                                        className: \"mb-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_27__[\"default\"].Check, {\n                                                                            type: \"checkbox\",\n                                                                            name: \"preferences.notifications\",\n                                                                            label: \"تفعيل الإشعارات\",\n                                                                            checked: formData.preferences.notifications,\n                                                                            onChange: handleInputChange,\n                                                                            disabled: !editing\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                variant: \"secondary\",\n                                                                className: \"me-2\",\n                                                                onClick: ()=>setEditing(false),\n                                                                children: \"إلغاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Badge_Button_Card_Col_Container_Form_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                type: \"submit\",\n                                                                variant: \"primary\",\n                                                                disabled: loading,\n                                                                children: loading ? \"جاري الحفظ...\" : \"حفظ التغييرات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                                lineNumber: 246,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\profile\\\\page.js\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/profile/page.js\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Navbar.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Nav.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Dropdown.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AppNavbar() {\n    const { user, isAuthenticated, logout, isAdmin, isSuperAdmin } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { isDark, toggleTheme, mounted } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLogout = ()=>{\n        logout();\n        setExpanded(false);\n    };\n    // تجنب hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            expand: \"lg\",\n            className: \"shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Brand, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        expand: \"lg\",\n        className: `shadow-sm ${isDark ? \"navbar-dark bg-dark\" : \"navbar-light bg-white\"}`,\n        expanded: expanded,\n        onToggle: setExpanded,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"navbar-brand text-decoration-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Toggle, {\n                    \"aria-controls\": \"basic-navbar-nav\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Collapse, {\n                    id: \"basic-navbar-nav\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"me-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"nav-link\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/courses\",\n                                    className: \"nav-link\",\n                                    children: \"الدورات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/leaderboard\",\n                                    className: \"nav-link\",\n                                    children: \"لوحة الشرف\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/my-courses\",\n                                            className: \"nav-link\",\n                                            children: \"دوراتي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile\",\n                                            className: \"nav-link\",\n                                            children: \"الملف الشخصي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"لوحة الإدارة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإحصائيات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                isSuperAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    bg: \"warning\",\n                                                    className: \"ms-1\",\n                                                    children: \"Super\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"الإدارة العليا\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"لوحة التحكم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إعدادات النظام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"d-flex align-items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"outline-secondary\",\n                                    size: \"sm\",\n                                    onClick: toggleTheme,\n                                    className: \"d-flex align-items-center\",\n                                    children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 25\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            variant: \"outline-primary\",\n                                            size: \"sm\",\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الملف الشخصي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Divider, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تسجيل الخروج\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"outline-primary\",\n                                                size: \"sm\",\n                                                children: \"تسجيل الدخول\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"primary\",\n                                                size: \"sm\",\n                                                children: \"إنشاء حساب\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL05hdmJhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVpQztBQUNpRDtBQUNyRDtBQUNxQjtBQUNFO0FBWTlCO0FBRVAsU0FBU29CO0lBQ3RCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxlQUFlLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUUsR0FBR2pCLDhEQUFPQTtJQUN4RSxNQUFNLEVBQUVrQixNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUduQixnRUFBUUE7SUFDakQsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHOUIsK0NBQVFBLENBQUM7SUFFekMsTUFBTStCLGVBQWU7UUFDbkJSO1FBQ0FPLFlBQVk7SUFDZDtJQUVBLDBCQUEwQjtJQUMxQixJQUFJLENBQUNGLFNBQVM7UUFDWixxQkFDRSw4REFBQzNCLHlIQUFNQTtZQUFDK0IsUUFBTztZQUFLQyxXQUFVO3NCQUM1Qiw0RUFBQzlCLHlIQUFTQTswQkFDUiw0RUFBQ0YseUhBQU1BLENBQUNpQyxLQUFLOztzQ0FDWCw4REFBQ3hCLHVKQUFhQTs0QkFBQ3lCLE1BQU07NEJBQUlGLFdBQVU7Ozs7Ozt3QkFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNdEQ7SUFFQSxxQkFDRSw4REFBQ2hDLHlIQUFNQTtRQUNMK0IsUUFBTztRQUNQQyxXQUFXLENBQUMsVUFBVSxFQUFFUCxTQUFTLHdCQUF3Qix3QkFBd0IsQ0FBQztRQUNsRkcsVUFBVUE7UUFDVk8sVUFBVU47a0JBRVYsNEVBQUMzQix5SEFBU0E7OzhCQUNSLDhEQUFDSSxrREFBSUE7b0JBQUM4QixNQUFLO29CQUFJSixXQUFVOztzQ0FDdkIsOERBQUN2Qix1SkFBYUE7NEJBQUN5QixNQUFNOzRCQUFJRixXQUFVOzs7Ozs7d0JBQVM7Ozs7Ozs7OEJBSTlDLDhEQUFDaEMseUhBQU1BLENBQUNxQyxNQUFNO29CQUFDQyxpQkFBYzs7Ozs7OzhCQUU3Qiw4REFBQ3RDLHlIQUFNQSxDQUFDdUMsUUFBUTtvQkFBQ0MsSUFBRzs7c0NBQ2xCLDhEQUFDdkMseUhBQUdBOzRCQUFDK0IsV0FBVTs7OENBQ2IsOERBQUMxQixrREFBSUE7b0NBQUM4QixNQUFLO29DQUFJSixXQUFVOzhDQUFXOzs7Ozs7OENBQ3BDLDhEQUFDMUIsa0RBQUlBO29DQUFDOEIsTUFBSztvQ0FBV0osV0FBVTs4Q0FBVzs7Ozs7OzhDQUMzQyw4REFBQzFCLGtEQUFJQTtvQ0FBQzhCLE1BQUs7b0NBQWVKLFdBQVU7OENBQVc7Ozs7OztnQ0FFOUNYLGlDQUNDOztzREFDRSw4REFBQ2Ysa0RBQUlBOzRDQUFDOEIsTUFBSzs0Q0FBY0osV0FBVTtzREFBVzs7Ozs7O3NEQUM5Qyw4REFBQzFCLGtEQUFJQTs0Q0FBQzhCLE1BQUs7NENBQVdKLFdBQVU7c0RBQVc7Ozs7Ozs7O2dDQUs5Q1QseUJBQ0MsOERBQUNuQix5SEFBUUE7b0NBQUNxQyxJQUFJeEMseUhBQUdBLENBQUN5QyxJQUFJOztzREFDcEIsOERBQUN0Qyx5SEFBUUEsQ0FBQ2lDLE1BQU07NENBQUNJLElBQUl4Qyx5SEFBR0EsQ0FBQ0ssSUFBSTs0Q0FBRTBCLFdBQVU7OzhEQUN2Qyw4REFBQ2Ysd0pBQU1BO29EQUFDaUIsTUFBTTtvREFBSUYsV0FBVTs7Ozs7O2dEQUFTOzs7Ozs7O3NEQUd2Qyw4REFBQzVCLHlIQUFRQSxDQUFDdUMsSUFBSTs7OERBQ1osOERBQUNyQyxrREFBSUE7b0RBQUM4QixNQUFLO29EQUFTSixXQUFVOztzRUFDNUIsOERBQUNkLHdKQUFTQTs0REFBQ2dCLE1BQU07NERBQUlGLFdBQVU7Ozs7Ozt3REFBUzs7Ozs7Ozs4REFHMUMsOERBQUMxQixrREFBSUE7b0RBQUM4QixNQUFLO29EQUFpQkosV0FBVTs7c0VBQ3BDLDhEQUFDakIsd0pBQVFBOzREQUFDbUIsTUFBTTs0REFBSUYsV0FBVTs7Ozs7O3dEQUFTOzs7Ozs7OzhEQUd6Qyw4REFBQzFCLGtEQUFJQTtvREFBQzhCLE1BQUs7b0RBQWVKLFdBQVU7O3NFQUNsQyw4REFBQ2hCLHdKQUFLQTs0REFBQ2tCLE1BQU07NERBQUlGLFdBQVU7Ozs7Ozt3REFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FRM0NSLDhCQUNDLDhEQUFDcEIseUhBQVFBO29DQUFDcUMsSUFBSXhDLHlIQUFHQSxDQUFDeUMsSUFBSTs7c0RBQ3BCLDhEQUFDdEMseUhBQVFBLENBQUNpQyxNQUFNOzRDQUFDSSxJQUFJeEMseUhBQUdBLENBQUNLLElBQUk7NENBQUUwQixXQUFVOzs4REFDdkMsOERBQUNmLHdKQUFNQTtvREFBQ2lCLE1BQU07b0RBQUlGLFdBQVU7Ozs7Ozs4REFDNUIsOERBQUMzQiwwSEFBS0E7b0RBQUN1QyxJQUFHO29EQUFVWixXQUFVOzhEQUFPOzs7Ozs7Z0RBQWE7Ozs7Ozs7c0RBR3BELDhEQUFDNUIseUhBQVFBLENBQUN1QyxJQUFJOzs4REFDWiw4REFBQ3JDLGtEQUFJQTtvREFBQzhCLE1BQUs7b0RBQWVKLFdBQVU7O3NFQUNsQyw4REFBQ2Qsd0pBQVNBOzREQUFDZ0IsTUFBTTs0REFBSUYsV0FBVTs7Ozs7O3dEQUFTOzs7Ozs7OzhEQUcxQyw4REFBQzFCLGtEQUFJQTtvREFBQzhCLE1BQUs7b0RBQXFCSixXQUFVOztzRUFDeEMsOERBQUNoQix3SkFBS0E7NERBQUNrQixNQUFNOzREQUFJRixXQUFVOzs7Ozs7d0RBQVM7Ozs7Ozs7OERBR3RDLDhEQUFDMUIsa0RBQUlBO29EQUFDOEIsTUFBSztvREFBdUJKLFdBQVU7O3NFQUMxQyw4REFBQ2pCLHdKQUFRQTs0REFBQ21CLE1BQU07NERBQUlGLFdBQVU7Ozs7Ozt3REFBUzs7Ozs7Ozs4REFHekMsOERBQUMxQixrREFBSUE7b0RBQUM4QixNQUFLO29EQUF3QkosV0FBVTs7c0VBQzNDLDhEQUFDcEIsd0pBQVFBOzREQUFDc0IsTUFBTTs0REFBSUYsV0FBVTs7Ozs7O3dEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVFqRCw4REFBQy9CLHlIQUFHQTs0QkFBQytCLFdBQVU7OzhDQUViLDhEQUFDN0IsMEhBQU1BO29DQUNMMEMsU0FBUTtvQ0FDUlgsTUFBSztvQ0FDTFksU0FBU3BCO29DQUNUTSxXQUFVOzhDQUVUUCx1QkFBUyw4REFBQ1osd0pBQUdBO3dDQUFDcUIsTUFBTTs7Ozs7NkRBQVMsOERBQUNwQix3SkFBSUE7d0NBQUNvQixNQUFNOzs7Ozs7Ozs7OztnQ0FHM0NiLG1CQUFtQkQscUJBQ2xCLDhEQUFDaEIseUhBQVFBO29DQUFDMkMsT0FBTTs7c0RBQ2QsOERBQUMzQyx5SEFBUUEsQ0FBQ2lDLE1BQU07NENBQ2RRLFNBQVE7NENBQ1JYLE1BQUs7NENBQ0xGLFdBQVU7OzhEQUVWLDhEQUFDdEIsd0pBQUlBO29EQUFDd0IsTUFBTTtvREFBSUYsV0FBVTs7Ozs7O2dEQUN6QlosS0FBSzRCLElBQUk7Ozs7Ozs7c0RBRVosOERBQUM1Qyx5SEFBUUEsQ0FBQ3VDLElBQUk7OzhEQUNaLDhEQUFDckMsa0RBQUlBO29EQUFDOEIsTUFBSztvREFBV0osV0FBVTs7c0VBQzlCLDhEQUFDdEIsd0pBQUlBOzREQUFDd0IsTUFBTTs0REFBSUYsV0FBVTs7Ozs7O3dEQUFTOzs7Ozs7OzhEQUdyQyw4REFBQzFCLGtEQUFJQTtvREFBQzhCLE1BQUs7b0RBQVlKLFdBQVU7O3NFQUMvQiw4REFBQ3BCLHdKQUFRQTs0REFBQ3NCLE1BQU07NERBQUlGLFdBQVU7Ozs7Ozt3REFBUzs7Ozs7Ozs4REFHekMsOERBQUM1Qix5SEFBUUEsQ0FBQzZDLE9BQU87Ozs7OzhEQUNqQiw4REFBQzdDLHlIQUFRQSxDQUFDc0MsSUFBSTtvREFBQ0ksU0FBU2hCOztzRUFDdEIsOERBQUNuQix3SkFBTUE7NERBQUN1QixNQUFNOzREQUFJRixXQUFVOzs7Ozs7d0RBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt5REFNM0MsOERBQUNrQjtvQ0FBSWxCLFdBQVU7O3NEQUNiLDhEQUFDMUIsa0RBQUlBOzRDQUFDOEIsTUFBSztzREFDVCw0RUFBQ2pDLDBIQUFNQTtnREFBQzBDLFNBQVE7Z0RBQWtCWCxNQUFLOzBEQUFLOzs7Ozs7Ozs7OztzREFJOUMsOERBQUM1QixrREFBSUE7NENBQUM4QixNQUFLO3NEQUNULDRFQUFDakMsMEhBQU1BO2dEQUFDMEMsU0FBUTtnREFBVVgsTUFBSzswREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVd0RCIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vY29tcG9uZW50cy9OYXZiYXIuanM/ZmJjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTmF2YmFyLCBOYXYsIENvbnRhaW5lciwgQnV0dG9uLCBEcm9wZG93biwgQmFkZ2UgfSBmcm9tICdyZWFjdC1ib290c3RyYXAnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuLi9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XG5pbXBvcnQgeyBcbiAgR3JhZHVhdGlvbkNhcCwgXG4gIFVzZXIsIFxuICBMb2dPdXQsIFxuICBTZXR0aW5ncywgXG4gIFN1biwgXG4gIE1vb24sXG4gIEJvb2tPcGVuLFxuICBVc2VycyxcbiAgU2hpZWxkLFxuICBCYXJDaGFydDNcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwTmF2YmFyKCkge1xuICBjb25zdCB7IHVzZXIsIGlzQXV0aGVudGljYXRlZCwgbG9nb3V0LCBpc0FkbWluLCBpc1N1cGVyQWRtaW4gfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgeyBpc0RhcmssIHRvZ2dsZVRoZW1lLCBtb3VudGVkIH0gPSB1c2VUaGVtZSgpO1xuICBjb25zdCBbZXhwYW5kZWQsIHNldEV4cGFuZGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSAoKSA9PiB7XG4gICAgbG9nb3V0KCk7XG4gICAgc2V0RXhwYW5kZWQoZmFsc2UpO1xuICB9O1xuXG4gIC8vINiq2KzZhtioIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICBpZiAoIW1vdW50ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPE5hdmJhciBleHBhbmQ9XCJsZ1wiIGNsYXNzTmFtZT1cInNoYWRvdy1zbVwiPlxuICAgICAgICA8Q29udGFpbmVyPlxuICAgICAgICAgIDxOYXZiYXIuQnJhbmQ+XG4gICAgICAgICAgICA8R3JhZHVhdGlvbkNhcCBzaXplPXsyNH0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICDYo9mD2KfYr9mK2YXZitipINin2YTYqti52YTZhVxuICAgICAgICAgIDwvTmF2YmFyLkJyYW5kPlxuICAgICAgICA8L0NvbnRhaW5lcj5cbiAgICAgIDwvTmF2YmFyPlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxOYXZiYXIgXG4gICAgICBleHBhbmQ9XCJsZ1wiIFxuICAgICAgY2xhc3NOYW1lPXtgc2hhZG93LXNtICR7aXNEYXJrID8gJ25hdmJhci1kYXJrIGJnLWRhcmsnIDogJ25hdmJhci1saWdodCBiZy13aGl0ZSd9YH1cbiAgICAgIGV4cGFuZGVkPXtleHBhbmRlZH1cbiAgICAgIG9uVG9nZ2xlPXtzZXRFeHBhbmRlZH1cbiAgICA+XG4gICAgICA8Q29udGFpbmVyPlxuICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cIm5hdmJhci1icmFuZCB0ZXh0LWRlY29yYXRpb24tbm9uZVwiPlxuICAgICAgICAgIDxHcmFkdWF0aW9uQ2FwIHNpemU9ezI0fSBjbGFzc05hbWU9XCJtZS0yXCIgLz5cbiAgICAgICAgICDYo9mD2KfYr9mK2YXZitipINin2YTYqti52YTZhVxuICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgPE5hdmJhci5Ub2dnbGUgYXJpYS1jb250cm9scz1cImJhc2ljLW5hdmJhci1uYXZcIiAvPlxuICAgICAgICBcbiAgICAgICAgPE5hdmJhci5Db2xsYXBzZSBpZD1cImJhc2ljLW5hdmJhci1uYXZcIj5cbiAgICAgICAgICA8TmF2IGNsYXNzTmFtZT1cIm1lLWF1dG9cIj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwibmF2LWxpbmtcIj7Yp9mE2LHYptmK2LPZitipPC9MaW5rPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jb3Vyc2VzXCIgY2xhc3NOYW1lPVwibmF2LWxpbmtcIj7Yp9mE2K/ZiNix2KfYqjwvTGluaz5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbGVhZGVyYm9hcmRcIiBjbGFzc05hbWU9XCJuYXYtbGlua1wiPtmE2YjYrdipINin2YTYtNix2YE8L0xpbms+XG5cbiAgICAgICAgICAgIHtpc0F1dGhlbnRpY2F0ZWQgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbXktY291cnNlc1wiIGNsYXNzTmFtZT1cIm5hdi1saW5rXCI+2K/ZiNix2KfYqtmKPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcHJvZmlsZVwiIGNsYXNzTmFtZT1cIm5hdi1saW5rXCI+2KfZhNmF2YTZgSDYp9mE2LTYrti12Yo8L0xpbms+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qINix2YjYp9io2Lcg2KfZhNmF2K/ZitixICovfVxuICAgICAgICAgICAge2lzQWRtaW4gJiYgKFxuICAgICAgICAgICAgICA8RHJvcGRvd24gYXM9e05hdi5JdGVtfT5cbiAgICAgICAgICAgICAgICA8RHJvcGRvd24uVG9nZ2xlIGFzPXtOYXYuTGlua30gY2xhc3NOYW1lPVwiZC1mbGV4IGFsaWduLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFNoaWVsZCBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMVwiIC8+XG4gICAgICAgICAgICAgICAgICDZhNmI2K3YqSDYp9mE2KXYr9in2LHYqVxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd24uVG9nZ2xlPlxuICAgICAgICAgICAgICAgIDxEcm9wZG93bi5NZW51PlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pblwiIGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgINin2YTYpdit2LXYp9im2YrYp9iqXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL2NvdXJzZXNcIiBjbGFzc05hbWU9XCJkcm9wZG93bi1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgINil2K/Yp9ix2Kkg2KfZhNiv2YjYsdin2KpcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vdXNlcnNcIiBjbGFzc05hbWU9XCJkcm9wZG93bi1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxVc2VycyBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgINil2K/Yp9ix2Kkg2KfZhNmF2LPYqtiu2K/ZhdmK2YZcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duLk1lbnU+XG4gICAgICAgICAgICAgIDwvRHJvcGRvd24+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7Lyog2LHZiNin2KjYtyDYp9mE2YXYr9mK2LEg2KfZhNi52KfZhSAqL31cbiAgICAgICAgICAgIHtpc1N1cGVyQWRtaW4gJiYgKFxuICAgICAgICAgICAgICA8RHJvcGRvd24gYXM9e05hdi5JdGVtfT5cbiAgICAgICAgICAgICAgICA8RHJvcGRvd24uVG9nZ2xlIGFzPXtOYXYuTGlua30gY2xhc3NOYW1lPVwiZC1mbGV4IGFsaWduLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFNoaWVsZCBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMVwiIC8+XG4gICAgICAgICAgICAgICAgICA8QmFkZ2UgYmc9XCJ3YXJuaW5nXCIgY2xhc3NOYW1lPVwibXMtMVwiPlN1cGVyPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgINin2YTYpdiv2KfYsdipINin2YTYudmE2YrYp1xuICAgICAgICAgICAgICAgIDwvRHJvcGRvd24uVG9nZ2xlPlxuICAgICAgICAgICAgICAgIDxEcm9wZG93bi5NZW51PlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9zdXBlci1hZG1pblwiIGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgINmE2YjYrdipINin2YTYqtit2YPZhVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9zdXBlci1hZG1pbi91c2Vyc1wiIGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXJzIHNpemU9ezE2fSBjbGFzc05hbWU9XCJtZS0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2KXYr9in2LHYqSDYp9mE2YXYs9iq2K7Yr9mF2YrZhlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9zdXBlci1hZG1pbi9jb3Vyc2VzXCIgY2xhc3NOYW1lPVwiZHJvcGRvd24taXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8Qm9va09wZW4gc2l6ZT17MTZ9IGNsYXNzTmFtZT1cIm1lLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDYpdiv2KfYsdipINin2YTYr9mI2LHYp9iqXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3N1cGVyLWFkbWluL3NldHRpbmdzXCIgY2xhc3NOYW1lPVwiZHJvcGRvd24taXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3Mgc2l6ZT17MTZ9IGNsYXNzTmFtZT1cIm1lLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDYpdi52K/Yp9iv2KfYqiDYp9mE2YbYuNin2YVcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duLk1lbnU+XG4gICAgICAgICAgICAgIDwvRHJvcGRvd24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvTmF2PlxuXG4gICAgICAgICAgPE5hdiBjbGFzc05hbWU9XCJkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICB7Lyog2LLYsSDYqti62YrZitixINin2YTYq9mK2YUgKi99XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lLXNlY29uZGFyeVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVRoZW1lfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzRGFyayA/IDxTdW4gc2l6ZT17MTZ9IC8+IDogPE1vb24gc2l6ZT17MTZ9IC8+fVxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIHtpc0F1dGhlbnRpY2F0ZWQgJiYgdXNlciA/IChcbiAgICAgICAgICAgICAgPERyb3Bkb3duIGFsaWduPVwiZW5kXCI+XG4gICAgICAgICAgICAgICAgPERyb3Bkb3duLlRvZ2dsZSBcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lLXByaW1hcnlcIiBcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VXNlciBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMVwiIC8+XG4gICAgICAgICAgICAgICAgICB7dXNlci5uYW1lfVxuICAgICAgICAgICAgICAgIDwvRHJvcGRvd24uVG9nZ2xlPlxuICAgICAgICAgICAgICAgIDxEcm9wZG93bi5NZW51PlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcm9maWxlXCIgY2xhc3NOYW1lPVwiZHJvcGRvd24taXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8VXNlciBzaXplPXsxNn0gY2xhc3NOYW1lPVwibWUtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgINin2YTZhdmE2YEg2KfZhNi02K7YtdmKXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NldHRpbmdzXCIgY2xhc3NOYW1lPVwiZHJvcGRvd24taXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3Mgc2l6ZT17MTZ9IGNsYXNzTmFtZT1cIm1lLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDYp9mE2KXYudiv2KfYr9in2KpcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bi5EaXZpZGVyIC8+XG4gICAgICAgICAgICAgICAgICA8RHJvcGRvd24uSXRlbSBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9PlxuICAgICAgICAgICAgICAgICAgICA8TG9nT3V0IHNpemU9ezE2fSBjbGFzc05hbWU9XCJtZS0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2KrYs9is2YrZhCDYp9mE2K7YsdmI2KxcbiAgICAgICAgICAgICAgICAgIDwvRHJvcGRvd24uSXRlbT5cbiAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duLk1lbnU+XG4gICAgICAgICAgICAgIDwvRHJvcGRvd24+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImQtZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbG9naW5cIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmUtcHJpbWFyeVwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgICAgICDYqtiz2KzZitmEINin2YTYr9iu2YjZhFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcmVnaXN0ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cInByaW1hcnlcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgICAg2KXZhti02KfYoSDYrdiz2KfYqFxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9OYXY+XG4gICAgICAgIDwvTmF2YmFyLkNvbGxhcHNlPlxuICAgICAgPC9Db250YWluZXI+XG4gICAgPC9OYXZiYXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJOYXZiYXIiLCJOYXYiLCJDb250YWluZXIiLCJCdXR0b24iLCJEcm9wZG93biIsIkJhZGdlIiwiTGluayIsInVzZUF1dGgiLCJ1c2VUaGVtZSIsIkdyYWR1YXRpb25DYXAiLCJVc2VyIiwiTG9nT3V0IiwiU2V0dGluZ3MiLCJTdW4iLCJNb29uIiwiQm9va09wZW4iLCJVc2VycyIsIlNoaWVsZCIsIkJhckNoYXJ0MyIsIkFwcE5hdmJhciIsInVzZXIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJsb2dvdXQiLCJpc0FkbWluIiwiaXNTdXBlckFkbWluIiwiaXNEYXJrIiwidG9nZ2xlVGhlbWUiLCJtb3VudGVkIiwiZXhwYW5kZWQiLCJzZXRFeHBhbmRlZCIsImhhbmRsZUxvZ291dCIsImV4cGFuZCIsImNsYXNzTmFtZSIsIkJyYW5kIiwic2l6ZSIsIm9uVG9nZ2xlIiwiaHJlZiIsIlRvZ2dsZSIsImFyaWEtY29udHJvbHMiLCJDb2xsYXBzZSIsImlkIiwiYXMiLCJJdGVtIiwiTWVudSIsImJnIiwidmFyaWFudCIsIm9uQ2xpY2siLCJhbGlnbiIsIm5hbWUiLCJEaXZpZGVyIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.js\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// إعداد Axios\nconst api = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"/api\"\n});\n// إضافة token للطلبات\napi.interceptors.request.use((config)=>{\n    if (false) {}\n    return config;\n});\n// معالجة انتهاء صلاحية Token\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== \"undefined\") {}\n    return Promise.reject(error);\n});\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initAuth();\n    }, []);\n    const initAuth = async ()=>{\n        if (true) {\n            setLoading(false);\n            return;\n        }\n        const token = localStorage.getItem(\"token\");\n        const localUser = localStorage.getItem(\"user\");\n        if (token && localUser) {\n            try {\n                const parsedUser = JSON.parse(localUser);\n                setUser(parsedUser);\n                setIsAuthenticated(true);\n                // التحقق من صحة Token\n                const response = await api.get(\"/auth/profile\");\n                setUser(response.data.user);\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                logout();\n            }\n        }\n        setLoading(false);\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await api.post(\"/auth/login\", {\n                email,\n                password\n            });\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل تسجيل الدخول\"\n            };\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            const response = await api.post(\"/auth/register\", userData);\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في التسجيل\"\n            };\n        }\n    };\n    const logout = ()=>{\n        if (false) {}\n        setUser(null);\n        setIsAuthenticated(false);\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await api.put(\"/auth/profile\", profileData);\n            const updatedUser = response.data.user;\n            setUser(updatedUser);\n            localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في تحديث البيانات\"\n            };\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        // Helper functions\n        isStudent: user?.role === \"student\",\n        isAdmin: user?.role === \"admin\",\n        isSuperAdmin: user?.role === \"super-admin\",\n        isAdminOrSuperAdmin: [\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role),\n        // API instance\n        api\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.js\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.js":
/*!**********************************!*\
  !*** ./contexts/ThemeContext.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من الثيم المحفوظ أو تفضيل النظام\n        const savedTheme = localStorage.getItem(\"theme\");\n        if (savedTheme) {\n            setIsDark(savedTheme === \"dark\");\n        } else {\n            setIsDark(window.matchMedia(\"(prefers-color-scheme: dark)\").matches);\n        }\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const theme = isDark ? \"dark\" : \"light\";\n        localStorage.setItem(\"theme\", theme);\n        document.body.setAttribute(\"data-bs-theme\", theme);\n        document.body.className = theme === \"dark\" ? \"bg-dark text-light\" : \"bg-light text-dark\";\n        // إضافة transition للتغيير السلس\n        document.documentElement.style.transition = \"background-color 0.3s, color 0.3s\";\n    }, [\n        isDark,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setIsDark((prev)=>!prev);\n    };\n    const value = {\n        isDark,\n        toggleTheme,\n        theme: isDark ? \"dark\" : \"light\",\n        mounted\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\ThemeContext.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.js\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"025f4d4086cc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hY2FkZW15LW5leHRqcy8uL2FwcC9nbG9iYWxzLmNzcz8xYmY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDI1ZjRkNDA4NmNjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\layout.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/profile/page.js":
/*!*****************************!*\
  !*** ./app/profile/page.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\profile\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/@restart","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/react-bootstrap","vendor-chunks/react-toastify","vendor-chunks/prop-types","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/react-transition-group","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/uncontrollable","vendor-chunks/@react-aria","vendor-chunks/dom-helpers","vendor-chunks/react-is","vendor-chunks/asynckit","vendor-chunks/react-lifecycles-compat","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/dequal","vendor-chunks/object-assign","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/warning","vendor-chunks/classnames","vendor-chunks/invariant","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/@babel","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/bootstrap","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();