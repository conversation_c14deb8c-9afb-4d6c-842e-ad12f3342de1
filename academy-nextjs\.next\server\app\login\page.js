/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.js */ \"(rsc)/./app/login/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(ssr)/./app/layout.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDbGF5b3V0LmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLz8wNTE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJkdWxtbm91bVxcXFxEb3dubG9hZHNcXFxccHJvamVjdC1ib2x0LWFjYWRlbXlcXFxccHJvamVjdFxcXFxhY2FkZW15LW5leHRqc1xcXFxhcHBcXFxcbGF5b3V0LmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clogin%5Cpage.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clogin%5Cpage.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.js */ \"(ssr)/./app/login/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDbG9naW4lNUNwYWdlLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLz9lZDBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJkdWxtbm91bVxcXFxEb3dubG9hZHNcXFxccHJvamVjdC1ib2x0LWFjYWRlbXlcXFxccHJvamVjdFxcXFxhY2FkZW15LW5leHRqc1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clogin%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"(ssr)/./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Navbar */ \"(ssr)/./components/Navbar.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-vh-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {\n                                position: \"top-right\",\n                                autoClose: 5000,\n                                hideProgressBar: false,\n                                newestOnTop: false,\n                                closeOnClick: true,\n                                rtl: true,\n                                pauseOnFocusLoss: true,\n                                draggable: true,\n                                pauseOnHover: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/layout.js\n");

/***/ }),

/***/ "(ssr)/./app/login/page.js":
/*!***************************!*\
  !*** ./app/login/page.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Spinner.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Form.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Container,Form,Row,Spinner!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,LogIn,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Login() {\n    const { login, isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // إعادة توجيه المستخدم المسجل دخوله\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && !authLoading) {\n            router.push(\"/\");\n        }\n    }, [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n        // إزالة رسالة الخطأ عند الكتابة\n        if (error) setError(\"\");\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        // التحقق من البيانات\n        if (!formData.email || !formData.password) {\n            setError(\"يرجى تعبئة جميع الحقول\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const result = await login(formData.email, formData.password);\n            if (result.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.success(result.message);\n                router.push(\"/\");\n            } else {\n                setError(result.message);\n            }\n        } catch (error) {\n            setError(\"حدث خطأ غير متوقع\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // عرض loading أثناء التحقق من المصادقة\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"d-flex justify-content-center align-items-center min-vh-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                animation: \"border\",\n                variant: \"primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-vh-100 d-flex align-items-center ${isDark ? \"bg-dark\" : \"bg-light\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"justify-content-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    md: 6,\n                    lg: 5,\n                    xl: 4,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: `shadow-lg border-0 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Body, {\n                            className: \"p-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-primary mb-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                size: 48\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                lineNumber: 87,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"fw-bold\",\n                                            children: \"تسجيل الدخول\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"مرحباً بك مرة أخرى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    variant: \"danger\",\n                                    className: \"mb-4\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    onSubmit: handleSubmit,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Group, {\n                                            className: \"mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Label, {\n                                                    children: \"البريد الإلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"position-relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Control, {\n                                                            type: \"email\",\n                                                            name: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل بريدك الإلكتروني\",\n                                                            required: true,\n                                                            className: \"pe-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"position-absolute top-50 translate-middle-y text-muted\",\n                                                            style: {\n                                                                right: \"12px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Group, {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Label, {\n                                                    children: \"كلمة المرور\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"position-relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"].Control, {\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            name: \"password\",\n                                                            value: formData.password,\n                                                            onChange: handleChange,\n                                                            placeholder: \"أدخل كلمة المرور\",\n                                                            required: true,\n                                                            className: \"pe-5 ps-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"position-absolute top-50 translate-middle-y text-muted\",\n                                                            style: {\n                                                                right: \"12px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            variant: \"link\",\n                                                            className: \"position-absolute top-50 translate-middle-y p-0 border-0 text-muted\",\n                                                            style: {\n                                                                left: \"12px\"\n                                                            },\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            type: \"button\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 41\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_LogIn_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 64\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"submit\",\n                                            variant: \"primary\",\n                                            size: \"lg\",\n                                            className: \"w-100 mb-3\",\n                                            disabled: loading,\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Container_Form_Row_Spinner_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: \"sm\",\n                                                        className: \"me-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"جاري تسجيل الدخول...\"\n                                                ]\n                                            }, void 0, true) : \"تسجيل الدخول\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-0\",\n                                                children: [\n                                                    \"ليس لديك حساب؟\",\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        href: \"/register\",\n                                                        className: \"text-primary text-decoration-none\",\n                                                        children: \"إنشاء حساب جديد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\login\\\\page.js\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.js\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Navbar.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Nav.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Dropdown.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AppNavbar() {\n    const { user, isAuthenticated, logout, isAdmin, isSuperAdmin } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { isDark, toggleTheme, mounted } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLogout = ()=>{\n        logout();\n        setExpanded(false);\n    };\n    // تجنب hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            expand: \"lg\",\n            className: \"shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Brand, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        expand: \"lg\",\n        className: `shadow-sm ${isDark ? \"navbar-dark bg-dark\" : \"navbar-light bg-white\"}`,\n        expanded: expanded,\n        onToggle: setExpanded,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"navbar-brand text-decoration-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Toggle, {\n                    \"aria-controls\": \"basic-navbar-nav\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Collapse, {\n                    id: \"basic-navbar-nav\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"me-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"nav-link\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/courses\",\n                                    className: \"nav-link\",\n                                    children: \"الدورات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/my-courses\",\n                                            className: \"nav-link\",\n                                            children: \"دوراتي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile\",\n                                            className: \"nav-link\",\n                                            children: \"الملف الشخصي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"لوحة الإدارة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإحصائيات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                isSuperAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    bg: \"warning\",\n                                                    className: \"ms-1\",\n                                                    children: \"Super\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"الإدارة العليا\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"لوحة التحكم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إعدادات النظام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"d-flex align-items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"outline-secondary\",\n                                    size: \"sm\",\n                                    onClick: toggleTheme,\n                                    className: \"d-flex align-items-center\",\n                                    children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 25\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            variant: \"outline-primary\",\n                                            size: \"sm\",\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الملف الشخصي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Divider, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تسجيل الخروج\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"outline-primary\",\n                                                size: \"sm\",\n                                                children: \"تسجيل الدخول\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"primary\",\n                                                size: \"sm\",\n                                                children: \"إنشاء حساب\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.js\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// إعداد Axios\nconst api = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"/api\"\n});\n// إضافة token للطلبات\napi.interceptors.request.use((config)=>{\n    if (false) {}\n    return config;\n});\n// معالجة انتهاء صلاحية Token\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== \"undefined\") {}\n    return Promise.reject(error);\n});\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initAuth();\n    }, []);\n    const initAuth = async ()=>{\n        if (true) {\n            setLoading(false);\n            return;\n        }\n        const token = localStorage.getItem(\"token\");\n        const localUser = localStorage.getItem(\"user\");\n        if (token && localUser) {\n            try {\n                const parsedUser = JSON.parse(localUser);\n                setUser(parsedUser);\n                setIsAuthenticated(true);\n                // التحقق من صحة Token\n                const response = await api.get(\"/auth/profile\");\n                setUser(response.data.user);\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                logout();\n            }\n        }\n        setLoading(false);\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await api.post(\"/auth/login\", {\n                email,\n                password\n            });\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل تسجيل الدخول\"\n            };\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            const response = await api.post(\"/auth/register\", userData);\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في التسجيل\"\n            };\n        }\n    };\n    const logout = ()=>{\n        if (false) {}\n        setUser(null);\n        setIsAuthenticated(false);\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await api.put(\"/auth/profile\", profileData);\n            const updatedUser = response.data.user;\n            setUser(updatedUser);\n            localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في تحديث البيانات\"\n            };\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        // Helper functions\n        isStudent: user?.role === \"student\",\n        isAdmin: user?.role === \"admin\",\n        isSuperAdmin: user?.role === \"super-admin\",\n        isAdminOrSuperAdmin: [\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role),\n        // API instance\n        api\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0cy9BdXRoQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV1RTtBQUM3QztBQUUxQixNQUFNSyw0QkFBY0wsb0RBQWFBO0FBRTFCLE1BQU1NLFVBQVU7SUFDckIsTUFBTUMsVUFBVU4saURBQVVBLENBQUNJO0lBQzNCLElBQUksQ0FBQ0UsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtBQUVGLGNBQWM7QUFDZCxNQUFNRSxNQUFNTCw2Q0FBS0EsQ0FBQ00sTUFBTSxDQUFDO0lBQ3ZCQyxTQUFTO0FBQ1g7QUFFQSxzQkFBc0I7QUFDdEJGLElBQUlHLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0M7SUFDNUIsSUFBSSxLQUFrQixFQUFhLEVBS2xDO0lBQ0QsT0FBT0E7QUFDVDtBQUVBLDZCQUE2QjtBQUM3Qk4sSUFBSUcsWUFBWSxDQUFDUyxRQUFRLENBQUNQLEdBQUcsQ0FDM0IsQ0FBQ08sV0FBYUEsVUFDZCxDQUFDQztJQUNDLElBQUlBLE1BQU1ELFFBQVEsRUFBRUUsV0FBVyxPQUFPLGdCQUFrQixhQUFhLEVBSXBFO0lBQ0QsT0FBT0ssUUFBUUMsTUFBTSxDQUFDUDtBQUN4QjtBQUdLLE1BQU1RLGVBQWUsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDdkMsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUMrQixTQUFTQyxXQUFXLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQUM7SUFFdkRELGdEQUFTQSxDQUFDO1FBQ1JvQztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1BLFdBQVc7UUFDZixJQUFJLElBQWtCLEVBQWE7WUFDakNILFdBQVc7WUFDWDtRQUNGO1FBRUEsTUFBTW5CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztRQUNuQyxNQUFNcUIsWUFBWXRCLGFBQWFDLE9BQU8sQ0FBQztRQUV2QyxJQUFJRixTQUFTdUIsV0FBVztZQUN0QixJQUFJO2dCQUNGLE1BQU1DLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0g7Z0JBQzlCTixRQUFRTztnQkFDUkgsbUJBQW1CO2dCQUVuQixzQkFBc0I7Z0JBQ3RCLE1BQU1oQixXQUFXLE1BQU1aLElBQUlrQyxHQUFHLENBQUM7Z0JBQy9CVixRQUFRWixTQUFTdUIsSUFBSSxDQUFDWixJQUFJO1lBQzVCLEVBQUUsT0FBT1YsT0FBTztnQkFDZHVCLFFBQVF2QixLQUFLLENBQUMsOEJBQThCQTtnQkFDNUN3QjtZQUNGO1FBQ0Y7UUFFQVgsV0FBVztJQUNiO0lBRUEsTUFBTVksUUFBUSxPQUFPQyxPQUFPQztRQUMxQixJQUFJO1lBQ0YsTUFBTTVCLFdBQVcsTUFBTVosSUFBSXlDLElBQUksQ0FBQyxlQUFlO2dCQUFFRjtnQkFBT0M7WUFBUztZQUNqRSxNQUFNLEVBQUVqQixJQUFJLEVBQUVoQixLQUFLLEVBQUUsR0FBR0ssU0FBU3VCLElBQUk7WUFFckMzQixhQUFha0MsT0FBTyxDQUFDLFNBQVNuQztZQUM5QkMsYUFBYWtDLE9BQU8sQ0FBQyxRQUFRVixLQUFLVyxTQUFTLENBQUNwQjtZQUU1Q0MsUUFBUUQ7WUFDUkssbUJBQW1CO1lBRW5CLE9BQU87Z0JBQUVnQixTQUFTO2dCQUFNQyxTQUFTakMsU0FBU3VCLElBQUksQ0FBQ1UsT0FBTztZQUFDO1FBQ3pELEVBQUUsT0FBT2hDLE9BQU87WUFDZCxPQUFPO2dCQUNMK0IsU0FBUztnQkFDVEMsU0FBU2hDLE1BQU1ELFFBQVEsRUFBRXVCLE1BQU1VLFdBQVc7WUFDNUM7UUFDRjtJQUNGO0lBRUEsTUFBTUMsV0FBVyxPQUFPQztRQUN0QixJQUFJO1lBQ0YsTUFBTW5DLFdBQVcsTUFBTVosSUFBSXlDLElBQUksQ0FBQyxrQkFBa0JNO1lBQ2xELE1BQU0sRUFBRXhCLElBQUksRUFBRWhCLEtBQUssRUFBRSxHQUFHSyxTQUFTdUIsSUFBSTtZQUVyQzNCLGFBQWFrQyxPQUFPLENBQUMsU0FBU25DO1lBQzlCQyxhQUFha0MsT0FBTyxDQUFDLFFBQVFWLEtBQUtXLFNBQVMsQ0FBQ3BCO1lBRTVDQyxRQUFRRDtZQUNSSyxtQkFBbUI7WUFFbkIsT0FBTztnQkFBRWdCLFNBQVM7Z0JBQU1DLFNBQVNqQyxTQUFTdUIsSUFBSSxDQUFDVSxPQUFPO1lBQUM7UUFDekQsRUFBRSxPQUFPaEMsT0FBTztZQUNkLE9BQU87Z0JBQ0wrQixTQUFTO2dCQUNUQyxTQUFTaEMsTUFBTUQsUUFBUSxFQUFFdUIsTUFBTVUsV0FBVztZQUM1QztRQUNGO0lBQ0Y7SUFFQSxNQUFNUixTQUFTO1FBQ2IsSUFBSSxLQUFrQixFQUFhLEVBR2xDO1FBQ0RiLFFBQVE7UUFDUkksbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTW9CLGdCQUFnQixPQUFPQztRQUMzQixJQUFJO1lBQ0YsTUFBTXJDLFdBQVcsTUFBTVosSUFBSWtELEdBQUcsQ0FBQyxpQkFBaUJEO1lBQ2hELE1BQU1FLGNBQWN2QyxTQUFTdUIsSUFBSSxDQUFDWixJQUFJO1lBRXRDQyxRQUFRMkI7WUFDUjNDLGFBQWFrQyxPQUFPLENBQUMsUUFBUVYsS0FBS1csU0FBUyxDQUFDUTtZQUU1QyxPQUFPO2dCQUFFUCxTQUFTO2dCQUFNQyxTQUFTakMsU0FBU3VCLElBQUksQ0FBQ1UsT0FBTztZQUFDO1FBQ3pELEVBQUUsT0FBT2hDLE9BQU87WUFDZCxPQUFPO2dCQUNMK0IsU0FBUztnQkFDVEMsU0FBU2hDLE1BQU1ELFFBQVEsRUFBRXVCLE1BQU1VLFdBQVc7WUFDNUM7UUFDRjtJQUNGO0lBRUEsTUFBTU8sUUFBUTtRQUNaN0I7UUFDQUk7UUFDQUY7UUFDQWE7UUFDQVE7UUFDQVQ7UUFDQVc7UUFDQSxtQkFBbUI7UUFDbkJLLFdBQVc5QixNQUFNK0IsU0FBUztRQUMxQkMsU0FBU2hDLE1BQU0rQixTQUFTO1FBQ3hCRSxjQUFjakMsTUFBTStCLFNBQVM7UUFDN0JHLHFCQUFxQjtZQUFDO1lBQVM7U0FBYyxDQUFDQyxRQUFRLENBQUNuQyxNQUFNK0I7UUFDN0QsZUFBZTtRQUNmdEQ7SUFDRjtJQUVBLHFCQUNFLDhEQUFDSixZQUFZK0QsUUFBUTtRQUFDUCxPQUFPQTtrQkFDMUI5Qjs7Ozs7O0FBR1AsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vY29udGV4dHMvQXV0aENvbnRleHQuanM/NTljZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG4vLyDYpdi52K/Yp9ivIEF4aW9zXG5jb25zdCBhcGkgPSBheGlvcy5jcmVhdGUoe1xuICBiYXNlVVJMOiAnL2FwaScsXG59KTtcblxuLy8g2KXYttin2YHYqSB0b2tlbiDZhNmE2LfZhNio2KfYqlxuYXBpLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZSgoY29uZmlnKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgaWYgKHRva2VuKSB7XG4gICAgICBjb25maWcuaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke3Rva2VufWA7XG4gICAgfVxuICB9XG4gIHJldHVybiBjb25maWc7XG59KTtcblxuLy8g2YXYudin2YTYrNipINin2YbYqtmH2KfYoSDYtdmE2KfYrdmK2KkgVG9rZW5cbmFwaS5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKFxuICAocmVzcG9uc2UpID0+IHJlc3BvbnNlLFxuICAoZXJyb3IpID0+IHtcbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxICYmIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldElzQXV0aGVudGljYXRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpbml0QXV0aCgpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaW5pdEF1dGggPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgIGNvbnN0IGxvY2FsVXNlciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XG5cbiAgICBpZiAodG9rZW4gJiYgbG9jYWxVc2VyKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJzZWRVc2VyID0gSlNPTi5wYXJzZShsb2NhbFVzZXIpO1xuICAgICAgICBzZXRVc2VyKHBhcnNlZFVzZXIpO1xuICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSk7XG4gICAgICAgIFxuICAgICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYtdit2KkgVG9rZW5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvYXV0aC9wcm9maWxlJyk7XG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UuZGF0YS51c2VyKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggaW5pdGlhbGl6YXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICBsb2dvdXQoKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgbG9naW4gPSBhc3luYyAoZW1haWwsIHBhc3N3b3JkKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoJy9hdXRoL2xvZ2luJywgeyBlbWFpbCwgcGFzc3dvcmQgfSk7XG4gICAgICBjb25zdCB7IHVzZXIsIHRva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXIpKTtcblxuICAgICAgc2V0VXNlcih1c2VyKTtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEJ1xuICAgICAgfTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGEpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdCgnL2F1dGgvcmVnaXN0ZXInLCB1c2VyRGF0YSk7XG4gICAgICBjb25zdCB7IHVzZXIsIHRva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXIpKTtcblxuICAgICAgc2V0VXNlcih1c2VyKTtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINmB2Yog2KfZhNiq2LPYrNmK2YQnXG4gICAgICB9O1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dvdXQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgfVxuICAgIHNldFVzZXIobnVsbCk7XG4gICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVQcm9maWxlID0gYXN5bmMgKHByb2ZpbGVEYXRhKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnB1dCgnL2F1dGgvcHJvZmlsZScsIHByb2ZpbGVEYXRhKTtcbiAgICAgIGNvbnN0IHVwZGF0ZWRVc2VyID0gcmVzcG9uc2UuZGF0YS51c2VyO1xuICAgICAgXG4gICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyJywgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFVzZXIpKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINmB2Yog2KrYrdiv2YrYqyDYp9mE2KjZitin2YbYp9iqJ1xuICAgICAgfTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgdXNlcixcbiAgICBpc0F1dGhlbnRpY2F0ZWQsXG4gICAgbG9hZGluZyxcbiAgICBsb2dpbixcbiAgICByZWdpc3RlcixcbiAgICBsb2dvdXQsXG4gICAgdXBkYXRlUHJvZmlsZSxcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb25zXG4gICAgaXNTdHVkZW50OiB1c2VyPy5yb2xlID09PSAnc3R1ZGVudCcsXG4gICAgaXNBZG1pbjogdXNlcj8ucm9sZSA9PT0gJ2FkbWluJyxcbiAgICBpc1N1cGVyQWRtaW46IHVzZXI/LnJvbGUgPT09ICdzdXBlci1hZG1pbicsXG4gICAgaXNBZG1pbk9yU3VwZXJBZG1pbjogWydhZG1pbicsICdzdXBlci1hZG1pbiddLmluY2x1ZGVzKHVzZXI/LnJvbGUpLFxuICAgIC8vIEFQSSBpbnN0YW5jZVxuICAgIGFwaVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImF4aW9zIiwiQXV0aENvbnRleHQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIiwiYXBpIiwiY3JlYXRlIiwiYmFzZVVSTCIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoZWFkZXJzIiwiQXV0aG9yaXphdGlvbiIsInJlc3BvbnNlIiwiZXJyb3IiLCJzdGF0dXMiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiUHJvbWlzZSIsInJlamVjdCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImlzQXV0aGVudGljYXRlZCIsInNldElzQXV0aGVudGljYXRlZCIsImluaXRBdXRoIiwibG9jYWxVc2VyIiwicGFyc2VkVXNlciIsIkpTT04iLCJwYXJzZSIsImdldCIsImRhdGEiLCJjb25zb2xlIiwibG9nb3V0IiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicG9zdCIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJzdWNjZXNzIiwibWVzc2FnZSIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiLCJwdXQiLCJ1cGRhdGVkVXNlciIsInZhbHVlIiwiaXNTdHVkZW50Iiwicm9sZSIsImlzQWRtaW4iLCJpc1N1cGVyQWRtaW4iLCJpc0FkbWluT3JTdXBlckFkbWluIiwiaW5jbHVkZXMiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.js\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.js":
/*!**********************************!*\
  !*** ./contexts/ThemeContext.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من الثيم المحفوظ أو تفضيل النظام\n        const savedTheme = localStorage.getItem(\"theme\");\n        if (savedTheme) {\n            setIsDark(savedTheme === \"dark\");\n        } else {\n            setIsDark(window.matchMedia(\"(prefers-color-scheme: dark)\").matches);\n        }\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const theme = isDark ? \"dark\" : \"light\";\n        localStorage.setItem(\"theme\", theme);\n        document.body.setAttribute(\"data-bs-theme\", theme);\n        document.body.className = theme === \"dark\" ? \"bg-dark text-light\" : \"bg-light text-dark\";\n        // إضافة transition للتغيير السلس\n        document.documentElement.style.transition = \"background-color 0.3s, color 0.3s\";\n    }, [\n        isDark,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setIsDark((prev)=>!prev);\n    };\n    const value = {\n        isDark,\n        toggleTheme,\n        theme: isDark ? \"dark\" : \"light\",\n        mounted\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\ThemeContext.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.js\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"025f4d4086cc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hY2FkZW15LW5leHRqcy8uL2FwcC9nbG9iYWxzLmNzcz8xYmY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDI1ZjRkNDA4NmNjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\layout.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/login/page.js":
/*!***************************!*\
  !*** ./app/login/page.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\login\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/@restart","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/react-bootstrap","vendor-chunks/react-toastify","vendor-chunks/prop-types","vendor-chunks/follow-redirects","vendor-chunks/react-transition-group","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/lucide-react","vendor-chunks/uncontrollable","vendor-chunks/@react-aria","vendor-chunks/dom-helpers","vendor-chunks/react-is","vendor-chunks/asynckit","vendor-chunks/react-lifecycles-compat","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/dequal","vendor-chunks/object-assign","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/warning","vendor-chunks/classnames","vendor-chunks/invariant","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/@babel","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/bootstrap","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();