/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/my-courses/page";
exports.ids = ["app/my-courses/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmy-courses%2Fpage&page=%2Fmy-courses%2Fpage&appPaths=%2Fmy-courses%2Fpage&pagePath=private-next-app-dir%2Fmy-courses%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmy-courses%2Fpage&page=%2Fmy-courses%2Fpage&appPaths=%2Fmy-courses%2Fpage&pagePath=private-next-app-dir%2Fmy-courses%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'my-courses',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/my-courses/page.js */ \"(rsc)/./app/my-courses/page.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/my-courses/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/my-courses/page\",\n        pathname: \"/my-courses\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmy-courses%2Fpage&page=%2Fmy-courses%2Fpage&appPaths=%2Fmy-courses%2Fpage&pagePath=private-next-app-dir%2Fmy-courses%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!":
/*!************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true! ***!
  \************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(ssr)/./app/layout.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDbGF5b3V0LmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLz8wNTE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWJkdWxtbm91bVxcXFxEb3dubG9hZHNcXFxccHJvamVjdC1ib2x0LWFjYWRlbXlcXFxccHJvamVjdFxcXFxhY2FkZW15LW5leHRqc1xcXFxhcHBcXFxcbGF5b3V0LmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Clayout.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cmy-courses%5Cpage.js&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cmy-courses%5Cpage.js&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/my-courses/page.js */ \"(ssr)/./app/my-courses/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQWJkdWxtbm91bSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtYm9sdC1hY2FkZW15JTVDcHJvamVjdCU1Q2FjYWRlbXktbmV4dGpzJTVDYXBwJTVDbXktY291cnNlcyU1Q3BhZ2UuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWNhZGVteS1uZXh0anMvPzM4M2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBYmR1bG1ub3VtXFxcXERvd25sb2Fkc1xcXFxwcm9qZWN0LWJvbHQtYWNhZGVteVxcXFxwcm9qZWN0XFxcXGFjYWRlbXktbmV4dGpzXFxcXGFwcFxcXFxteS1jb3Vyc2VzXFxcXHBhZ2UuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp%5Cmy-courses%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"(ssr)/./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Navbar */ \"(ssr)/./components/Navbar.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(ssr)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-vh-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 22,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_6__.ToastContainer, {\n                                position: \"top-right\",\n                                autoClose: 5000,\n                                hideProgressBar: false,\n                                newestOnTop: false,\n                                closeOnClick: true,\n                                rtl: true,\n                                pauseOnFocusLoss: true,\n                                draggable: true,\n                                pauseOnHover: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\layout.js\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/layout.js\n");

/***/ }),

/***/ "(ssr)/./app/my-courses/page.js":
/*!********************************!*\
  !*** ./app/my-courses/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyCourses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Row.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Col.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/ProgressBar.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Card,Col,Container,ProgressBar,Row,Tab,Tabs!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Calendar,CheckCircle,Clock,Play,Star,Target,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction MyCourses() {\n    const { user, isAuthenticated, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { isDark } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"in-progress\");\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        inProgress: [],\n        completed: [],\n        wishlist: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCourses: 0,\n        completedCourses: 0,\n        totalHours: 0,\n        certificates: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !isAuthenticated) {\n            router.push(\"/login\");\n            return;\n        }\n        if (isAuthenticated) {\n            fetchMyCourses();\n        }\n    }, [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    const fetchMyCourses = async ()=>{\n        try {\n            setLoading(true);\n            // محاكاة بيانات دورات المستخدم\n            const mockCourses = {\n                inProgress: [\n                    {\n                        _id: \"1\",\n                        title: \"تطوير تطبيقات React المتقدمة\",\n                        instructor: \"أحمد المطور\",\n                        progress: 65,\n                        totalLessons: 24,\n                        completedLessons: 16,\n                        lastAccessed: new Date(),\n                        estimatedTime: \"8 ساعات متبقية\",\n                        level: \"متوسط\",\n                        category: \"البرمجة\"\n                    },\n                    {\n                        _id: \"2\",\n                        title: \"تصميم واجهات المستخدم\",\n                        instructor: \"سارة المصممة\",\n                        progress: 30,\n                        totalLessons: 18,\n                        completedLessons: 5,\n                        lastAccessed: new Date(Date.now() - 86400000),\n                        estimatedTime: \"12 ساعة متبقية\",\n                        level: \"مبتدئ\",\n                        category: \"التصميم\"\n                    },\n                    {\n                        _id: \"3\",\n                        title: \"أساسيات الأمن السيبراني\",\n                        instructor: \"محمد الخبير\",\n                        progress: 85,\n                        totalLessons: 15,\n                        completedLessons: 13,\n                        lastAccessed: new Date(Date.now() - 172800000),\n                        estimatedTime: \"2 ساعة متبقية\",\n                        level: \"متقدم\",\n                        category: \"الأمن السيبراني\"\n                    }\n                ],\n                completed: [\n                    {\n                        _id: \"4\",\n                        title: \"أساسيات JavaScript\",\n                        instructor: \"علي المبرمج\",\n                        completedAt: new Date(Date.now() - 604800000),\n                        rating: 5,\n                        certificate: true,\n                        totalHours: 15,\n                        level: \"مبتدئ\",\n                        category: \"البرمجة\"\n                    },\n                    {\n                        _id: \"5\",\n                        title: \"مقدمة في قواعد البيانات\",\n                        instructor: \"فاطمة الخبيرة\",\n                        completedAt: new Date(Date.now() - 1209600000),\n                        rating: 4,\n                        certificate: true,\n                        totalHours: 12,\n                        level: \"مبتدئ\",\n                        category: \"قواعد البيانات\"\n                    }\n                ],\n                wishlist: [\n                    {\n                        _id: \"6\",\n                        title: \"تطوير تطبيقات الهاتف المحمول\",\n                        instructor: \"خالد المطور\",\n                        price: 399,\n                        rating: 4.8,\n                        students: 1200,\n                        level: \"متقدم\",\n                        category: \"تطوير التطبيقات\"\n                    }\n                ]\n            };\n            setCourses(mockCourses);\n            // حساب الإحصائيات\n            setStats({\n                totalCourses: mockCourses.inProgress.length + mockCourses.completed.length,\n                completedCourses: mockCourses.completed.length,\n                totalHours: mockCourses.completed.reduce((total, course)=>total + course.totalHours, 0),\n                certificates: mockCourses.completed.filter((course)=>course.certificate).length\n            });\n        } catch (error) {\n            console.error(\"Error fetching my courses:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getLevelBadge = (level)=>{\n        switch(level){\n            case \"مبتدئ\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    bg: \"success\",\n                    children: \"مبتدئ\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n            case \"متوسط\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    bg: \"warning\",\n                    children: \"متوسط\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"متقدم\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    bg: \"danger\",\n                    children: \"متقدم\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    bg: \"secondary\",\n                    children: level\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatLastAccessed = (date)=>{\n        const now = new Date();\n        const diff = now - date;\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days === 0) return \"اليوم\";\n        if (days === 1) return \"أمس\";\n        return `منذ ${days} أيام`;\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"d-flex justify-content-center align-items-center min-vh-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner-border text-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-vh-100 ${isDark ? \"bg-dark text-light\" : \"bg-light\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"py-5\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"mb-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"display-5 fw-bold mb-3\",\n                                    children: \"دوراتي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead text-muted\",\n                                    children: \"تابع تقدمك في الدورات واستكمل رحلتك التعليمية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"mb-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-primary mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"fw-bold\",\n                                            children: stats.totalCourses\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"إجمالي الدورات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-success mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"fw-bold\",\n                                            children: stats.completedCourses\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"دورة مكتملة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-info mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"fw-bold\",\n                                            children: stats.totalHours\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"ساعة تعلم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: `text-center border-0 shadow-sm h-100 ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-warning mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"fw-bold\",\n                                            children: stats.certificates\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-0\",\n                                            children: \"شهادة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: `border-0 shadow-sm ${isDark ? \"bg-dark text-light\" : \"\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        activeKey: activeTab,\n                                        onSelect: setActiveTab,\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                eventKey: \"in-progress\",\n                                                title: `قيد التقدم (${courses.inProgress.length})`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"g-4\",\n                                                    children: courses.inProgress.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            md: 6,\n                                                            lg: 4,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: `h-100 border-0 shadow-sm ${isDark ? \"bg-secondary\" : \"bg-white\"}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-start mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                    bg: \"primary\",\n                                                                                    children: course.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 254,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                getLevelBadge(course.level)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"fw-bold mb-2\",\n                                                                            children: course.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    size: 14,\n                                                                                    className: \"text-muted me-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 261,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: course.instructor\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 262,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"d-flex justify-content-between align-items-center mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"small\",\n                                                                                            children: \"التقدم\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 267,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"small fw-bold\",\n                                                                                            children: [\n                                                                                                course.progress,\n                                                                                                \"%\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 268,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    now: course.progress,\n                                                                                    variant: course.progress > 80 ? \"success\" : course.progress > 50 ? \"primary\" : \"warning\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 270,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: [\n                                                                                        course.completedLessons,\n                                                                                        \" من \",\n                                                                                        course.totalLessons,\n                                                                                        \" درس\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            size: 12,\n                                                                                            className: \"me-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 281,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        \"آخر وصول: \",\n                                                                                        formatLastAccessed(course.lastAccessed)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-info\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            size: 12,\n                                                                                            className: \"me-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 285,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        course.estimatedTime\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 284,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-grid\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                                href: `/courses/${course._id}`,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    variant: \"primary\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            size: 16,\n                                                                                            className: \"me-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 293,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"متابعة التعلم\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 292,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, course._id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                eventKey: \"completed\",\n                                                title: `مكتملة (${courses.completed.length})`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"g-4\",\n                                                    children: courses.completed.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            md: 6,\n                                                            lg: 4,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: `h-100 border-0 shadow-sm ${isDark ? \"bg-secondary\" : \"bg-white\"}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-start mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                    bg: \"success\",\n                                                                                    children: \"مكتملة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 312,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                course.certificate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    size: 20,\n                                                                                    className: \"text-warning\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"fw-bold mb-2\",\n                                                                            children: course.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    size: 14,\n                                                                                    className: \"text-muted me-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: course.instructor\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 322,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                            className: \"text-muted d-block\",\n                                                                                            children: \"تاريخ الإكمال\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 327,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                            children: course.completedAt.toLocaleDateString(\"ar-SA\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 328,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 326,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-end\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                            className: \"text-muted d-block\",\n                                                                                            children: \"المدة\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 331,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                            children: [\n                                                                                                course.totalHours,\n                                                                                                \" ساعة\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 332,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"me-2\",\n                                                                                    children: \"تقييمك:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 337,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"d-flex\",\n                                                                                    children: [\n                                                                                        ...Array(5)\n                                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            size: 14,\n                                                                                            className: i < course.rating ? \"text-warning\" : \"text-muted\"\n                                                                                        }, i, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 340,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 338,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-grid gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                                    href: `/courses/${course._id}`,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        variant: \"outline-primary\",\n                                                                                        children: \"مراجعة الدورة\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                        lineNumber: 351,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 350,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                course.certificate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    variant: \"success\",\n                                                                                    size: \"sm\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            size: 14,\n                                                                                            className: \"me-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 357,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"تحميل الشهادة\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 356,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, course._id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                eventKey: \"wishlist\",\n                                                title: `قائمة الأمنيات (${courses.wishlist.length})`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"g-4\",\n                                                    children: courses.wishlist.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            md: 6,\n                                                            lg: 4,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: `h-100 border-0 shadow-sm ${isDark ? \"bg-secondary\" : \"bg-white\"}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_10__[\"default\"].Body, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-start mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                    bg: \"info\",\n                                                                                    children: course.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 376,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                getLevelBadge(course.level)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"fw-bold mb-2\",\n                                                                            children: course.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    size: 14,\n                                                                                    className: \"text-muted me-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: course.instructor\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 384,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"d-flex align-items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            size: 14,\n                                                                                            className: \"text-warning me-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 389,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: course.rating\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                            lineNumber: 390,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                                    className: \"text-muted\",\n                                                                                    children: [\n                                                                                        course.students,\n                                                                                        \" طالب\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 392,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-flex justify-content-between align-items-center mb-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"fw-bold text-primary mb-0\",\n                                                                                children: [\n                                                                                    course.price,\n                                                                                    \" ر.س\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"d-grid gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                                    href: `/courses/${course._id}`,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        variant: \"primary\",\n                                                                                        children: \"عرض التفاصيل\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                        lineNumber: 405,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 404,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    variant: \"outline-danger\",\n                                                                                    size: \"sm\",\n                                                                                    children: \"إزالة من القائمة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                                    lineNumber: 409,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, course._id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === \"in-progress\" && courses.inProgress.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Calendar_CheckCircle_Clock_Play_Star_Target_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 64,\n                                                className: \"text-muted mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 424,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"لا توجد دورات قيد التقدم\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 425,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted mb-4\",\n                                                children: \"ابدأ رحلتك التعليمية بالتسجيل في دورة جديدة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/courses\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Card_Col_Container_ProgressBar_Row_Tab_Tabs_react_bootstrap__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    variant: \"primary\",\n                                                    children: \"تصفح الدورات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                                lineNumber: 427,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                        lineNumber: 423,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\app\\\\my-courses\\\\page.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/my-courses/page.js\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.js":
/*!******************************!*\
  !*** ./components/Navbar.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Navbar.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Nav.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Dropdown.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Badge,Button,Container,Dropdown,Nav,Navbar!=!react-bootstrap */ \"(ssr)/./node_modules/react-bootstrap/esm/Button.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,GraduationCap,LogOut,Moon,Settings,Shield,Sun,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AppNavbar() {\n    const { user, isAuthenticated, logout, isAdmin, isSuperAdmin } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { isDark, toggleTheme, mounted } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleLogout = ()=>{\n        logout();\n        setExpanded(false);\n    };\n    // تجنب hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            expand: \"lg\",\n            className: \"shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Brand, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        expand: \"lg\",\n        className: `shadow-sm ${isDark ? \"navbar-dark bg-dark\" : \"navbar-light bg-white\"}`,\n        expanded: expanded,\n        onToggle: setExpanded,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"navbar-brand text-decoration-none\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24,\n                            className: \"me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        \"أكاديمية التعلم\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Toggle, {\n                    \"aria-controls\": \"basic-navbar-nav\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Collapse, {\n                    id: \"basic-navbar-nav\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"me-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"nav-link\",\n                                    children: \"الرئيسية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/courses\",\n                                    className: \"nav-link\",\n                                    children: \"الدورات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/leaderboard\",\n                                    className: \"nav-link\",\n                                    children: \"لوحة الشرف\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/my-courses\",\n                                            className: \"nav-link\",\n                                            children: \"دوراتي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/profile\",\n                                            className: \"nav-link\",\n                                            children: \"الملف الشخصي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"لوحة الإدارة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإحصائيات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                isSuperAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Item,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            as: _barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"].Link,\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    bg: \"warning\",\n                                                    className: \"ms-1\",\n                                                    children: \"Super\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"الإدارة العليا\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"لوحة التحكم\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/users\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة المستخدمين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/courses\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إدارة الدورات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/super-admin/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"إعدادات النظام\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"d-flex align-items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"outline-secondary\",\n                                    size: \"sm\",\n                                    onClick: toggleTheme,\n                                    className: \"d-flex align-items-center\",\n                                    children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 25\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                isAuthenticated && user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Toggle, {\n                                            variant: \"outline-primary\",\n                                            size: \"sm\",\n                                            className: \"d-flex align-items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"me-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Menu, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الملف الشخصي\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/settings\",\n                                                    className: \"dropdown-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"الإعدادات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Divider, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_9__[\"default\"].Item, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_GraduationCap_LogOut_Moon_Settings_Shield_Sun_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"me-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"تسجيل الخروج\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"outline-primary\",\n                                                size: \"sm\",\n                                                children: \"تسجيل الدخول\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_Button_Container_Dropdown_Nav_Navbar_react_bootstrap__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                variant: \"primary\",\n                                                size: \"sm\",\n                                                children: \"إنشاء حساب\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\components\\\\Navbar.js\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.js\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// إعداد Axios\nconst api = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: \"/api\"\n});\n// إضافة token للطلبات\napi.interceptors.request.use((config)=>{\n    if (false) {}\n    return config;\n});\n// معالجة انتهاء صلاحية Token\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== \"undefined\") {}\n    return Promise.reject(error);\n});\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initAuth();\n    }, []);\n    const initAuth = async ()=>{\n        if (true) {\n            setLoading(false);\n            return;\n        }\n        const token = localStorage.getItem(\"token\");\n        const localUser = localStorage.getItem(\"user\");\n        if (token && localUser) {\n            try {\n                const parsedUser = JSON.parse(localUser);\n                setUser(parsedUser);\n                setIsAuthenticated(true);\n                // التحقق من صحة Token\n                const response = await api.get(\"/auth/profile\");\n                setUser(response.data.user);\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                logout();\n            }\n        }\n        setLoading(false);\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await api.post(\"/auth/login\", {\n                email,\n                password\n            });\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل تسجيل الدخول\"\n            };\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            const response = await api.post(\"/auth/register\", userData);\n            const { user, token } = response.data;\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            setUser(user);\n            setIsAuthenticated(true);\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في التسجيل\"\n            };\n        }\n    };\n    const logout = ()=>{\n        if (false) {}\n        setUser(null);\n        setIsAuthenticated(false);\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await api.put(\"/auth/profile\", profileData);\n            const updatedUser = response.data.user;\n            setUser(updatedUser);\n            localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n            return {\n                success: true,\n                message: response.data.message\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: error.response?.data?.message || \"فشل في تحديث البيانات\"\n            };\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        // Helper functions\n        isStudent: user?.role === \"student\",\n        isAdmin: user?.role === \"admin\",\n        isSuperAdmin: user?.role === \"super-admin\",\n        isAdminOrSuperAdmin: [\n            \"admin\",\n            \"super-admin\"\n        ].includes(user?.role),\n        // API instance\n        api\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb250ZXh0cy9BdXRoQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUV1RTtBQUM3QztBQUUxQixNQUFNSyw0QkFBY0wsb0RBQWFBO0FBRTFCLE1BQU1NLFVBQVU7SUFDckIsTUFBTUMsVUFBVU4saURBQVVBLENBQUNJO0lBQzNCLElBQUksQ0FBQ0UsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtBQUVGLGNBQWM7QUFDZCxNQUFNRSxNQUFNTCw2Q0FBS0EsQ0FBQ00sTUFBTSxDQUFDO0lBQ3ZCQyxTQUFTO0FBQ1g7QUFFQSxzQkFBc0I7QUFDdEJGLElBQUlHLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0M7SUFDNUIsSUFBSSxLQUFrQixFQUFhLEVBS2xDO0lBQ0QsT0FBT0E7QUFDVDtBQUVBLDZCQUE2QjtBQUM3Qk4sSUFBSUcsWUFBWSxDQUFDUyxRQUFRLENBQUNQLEdBQUcsQ0FDM0IsQ0FBQ08sV0FBYUEsVUFDZCxDQUFDQztJQUNDLElBQUlBLE1BQU1ELFFBQVEsRUFBRUUsV0FBVyxPQUFPLGdCQUFrQixhQUFhLEVBSXBFO0lBQ0QsT0FBT0ssUUFBUUMsTUFBTSxDQUFDUDtBQUN4QjtBQUdLLE1BQU1RLGVBQWUsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDdkMsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNqQyxNQUFNLENBQUMrQixTQUFTQyxXQUFXLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQUM7SUFFdkRELGdEQUFTQSxDQUFDO1FBQ1JvQztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1BLFdBQVc7UUFDZixJQUFJLElBQWtCLEVBQWE7WUFDakNILFdBQVc7WUFDWDtRQUNGO1FBRUEsTUFBTW5CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztRQUNuQyxNQUFNcUIsWUFBWXRCLGFBQWFDLE9BQU8sQ0FBQztRQUV2QyxJQUFJRixTQUFTdUIsV0FBVztZQUN0QixJQUFJO2dCQUNGLE1BQU1DLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0g7Z0JBQzlCTixRQUFRTztnQkFDUkgsbUJBQW1CO2dCQUVuQixzQkFBc0I7Z0JBQ3RCLE1BQU1oQixXQUFXLE1BQU1aLElBQUlrQyxHQUFHLENBQUM7Z0JBQy9CVixRQUFRWixTQUFTdUIsSUFBSSxDQUFDWixJQUFJO1lBQzVCLEVBQUUsT0FBT1YsT0FBTztnQkFDZHVCLFFBQVF2QixLQUFLLENBQUMsOEJBQThCQTtnQkFDNUN3QjtZQUNGO1FBQ0Y7UUFFQVgsV0FBVztJQUNiO0lBRUEsTUFBTVksUUFBUSxPQUFPQyxPQUFPQztRQUMxQixJQUFJO1lBQ0YsTUFBTTVCLFdBQVcsTUFBTVosSUFBSXlDLElBQUksQ0FBQyxlQUFlO2dCQUFFRjtnQkFBT0M7WUFBUztZQUNqRSxNQUFNLEVBQUVqQixJQUFJLEVBQUVoQixLQUFLLEVBQUUsR0FBR0ssU0FBU3VCLElBQUk7WUFFckMzQixhQUFha0MsT0FBTyxDQUFDLFNBQVNuQztZQUM5QkMsYUFBYWtDLE9BQU8sQ0FBQyxRQUFRVixLQUFLVyxTQUFTLENBQUNwQjtZQUU1Q0MsUUFBUUQ7WUFDUkssbUJBQW1CO1lBRW5CLE9BQU87Z0JBQUVnQixTQUFTO2dCQUFNQyxTQUFTakMsU0FBU3VCLElBQUksQ0FBQ1UsT0FBTztZQUFDO1FBQ3pELEVBQUUsT0FBT2hDLE9BQU87WUFDZCxPQUFPO2dCQUNMK0IsU0FBUztnQkFDVEMsU0FBU2hDLE1BQU1ELFFBQVEsRUFBRXVCLE1BQU1VLFdBQVc7WUFDNUM7UUFDRjtJQUNGO0lBRUEsTUFBTUMsV0FBVyxPQUFPQztRQUN0QixJQUFJO1lBQ0YsTUFBTW5DLFdBQVcsTUFBTVosSUFBSXlDLElBQUksQ0FBQyxrQkFBa0JNO1lBQ2xELE1BQU0sRUFBRXhCLElBQUksRUFBRWhCLEtBQUssRUFBRSxHQUFHSyxTQUFTdUIsSUFBSTtZQUVyQzNCLGFBQWFrQyxPQUFPLENBQUMsU0FBU25DO1lBQzlCQyxhQUFha0MsT0FBTyxDQUFDLFFBQVFWLEtBQUtXLFNBQVMsQ0FBQ3BCO1lBRTVDQyxRQUFRRDtZQUNSSyxtQkFBbUI7WUFFbkIsT0FBTztnQkFBRWdCLFNBQVM7Z0JBQU1DLFNBQVNqQyxTQUFTdUIsSUFBSSxDQUFDVSxPQUFPO1lBQUM7UUFDekQsRUFBRSxPQUFPaEMsT0FBTztZQUNkLE9BQU87Z0JBQ0wrQixTQUFTO2dCQUNUQyxTQUFTaEMsTUFBTUQsUUFBUSxFQUFFdUIsTUFBTVUsV0FBVztZQUM1QztRQUNGO0lBQ0Y7SUFFQSxNQUFNUixTQUFTO1FBQ2IsSUFBSSxLQUFrQixFQUFhLEVBR2xDO1FBQ0RiLFFBQVE7UUFDUkksbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTW9CLGdCQUFnQixPQUFPQztRQUMzQixJQUFJO1lBQ0YsTUFBTXJDLFdBQVcsTUFBTVosSUFBSWtELEdBQUcsQ0FBQyxpQkFBaUJEO1lBQ2hELE1BQU1FLGNBQWN2QyxTQUFTdUIsSUFBSSxDQUFDWixJQUFJO1lBRXRDQyxRQUFRMkI7WUFDUjNDLGFBQWFrQyxPQUFPLENBQUMsUUFBUVYsS0FBS1csU0FBUyxDQUFDUTtZQUU1QyxPQUFPO2dCQUFFUCxTQUFTO2dCQUFNQyxTQUFTakMsU0FBU3VCLElBQUksQ0FBQ1UsT0FBTztZQUFDO1FBQ3pELEVBQUUsT0FBT2hDLE9BQU87WUFDZCxPQUFPO2dCQUNMK0IsU0FBUztnQkFDVEMsU0FBU2hDLE1BQU1ELFFBQVEsRUFBRXVCLE1BQU1VLFdBQVc7WUFDNUM7UUFDRjtJQUNGO0lBRUEsTUFBTU8sUUFBUTtRQUNaN0I7UUFDQUk7UUFDQUY7UUFDQWE7UUFDQVE7UUFDQVQ7UUFDQVc7UUFDQSxtQkFBbUI7UUFDbkJLLFdBQVc5QixNQUFNK0IsU0FBUztRQUMxQkMsU0FBU2hDLE1BQU0rQixTQUFTO1FBQ3hCRSxjQUFjakMsTUFBTStCLFNBQVM7UUFDN0JHLHFCQUFxQjtZQUFDO1lBQVM7U0FBYyxDQUFDQyxRQUFRLENBQUNuQyxNQUFNK0I7UUFDN0QsZUFBZTtRQUNmdEQ7SUFDRjtJQUVBLHFCQUNFLDhEQUFDSixZQUFZK0QsUUFBUTtRQUFDUCxPQUFPQTtrQkFDMUI5Qjs7Ozs7O0FBR1AsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2FjYWRlbXktbmV4dGpzLy4vY29udGV4dHMvQXV0aENvbnRleHQuanM/NTljZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKCFjb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuXG4vLyDYpdi52K/Yp9ivIEF4aW9zXG5jb25zdCBhcGkgPSBheGlvcy5jcmVhdGUoe1xuICBiYXNlVVJMOiAnL2FwaScsXG59KTtcblxuLy8g2KXYttin2YHYqSB0b2tlbiDZhNmE2LfZhNio2KfYqlxuYXBpLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZSgoY29uZmlnKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgaWYgKHRva2VuKSB7XG4gICAgICBjb25maWcuaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke3Rva2VufWA7XG4gICAgfVxuICB9XG4gIHJldHVybiBjb25maWc7XG59KTtcblxuLy8g2YXYudin2YTYrNipINin2YbYqtmH2KfYoSDYtdmE2KfYrdmK2KkgVG9rZW5cbmFwaS5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKFxuICAocmVzcG9uc2UpID0+IHJlc3BvbnNlLFxuICAoZXJyb3IpID0+IHtcbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxICYmIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpO1xuICB9XG4pO1xuXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldElzQXV0aGVudGljYXRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpbml0QXV0aCgpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaW5pdEF1dGggPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgIGNvbnN0IGxvY2FsVXNlciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XG5cbiAgICBpZiAodG9rZW4gJiYgbG9jYWxVc2VyKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBwYXJzZWRVc2VyID0gSlNPTi5wYXJzZShsb2NhbFVzZXIpO1xuICAgICAgICBzZXRVc2VyKHBhcnNlZFVzZXIpO1xuICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSk7XG4gICAgICAgIFxuICAgICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYtdit2KkgVG9rZW5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvYXV0aC9wcm9maWxlJyk7XG4gICAgICAgIHNldFVzZXIocmVzcG9uc2UuZGF0YS51c2VyKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggaW5pdGlhbGl6YXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICBsb2dvdXQoKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgbG9naW4gPSBhc3luYyAoZW1haWwsIHBhc3N3b3JkKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoJy9hdXRoL2xvZ2luJywgeyBlbWFpbCwgcGFzc3dvcmQgfSk7XG4gICAgICBjb25zdCB7IHVzZXIsIHRva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXIpKTtcblxuICAgICAgc2V0VXNlcih1c2VyKTtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEJ1xuICAgICAgfTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGEpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdCgnL2F1dGgvcmVnaXN0ZXInLCB1c2VyRGF0YSk7XG4gICAgICBjb25zdCB7IHVzZXIsIHRva2VuIH0gPSByZXNwb25zZS5kYXRhO1xuXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHVzZXIpKTtcblxuICAgICAgc2V0VXNlcih1c2VyKTtcbiAgICAgIHNldElzQXV0aGVudGljYXRlZCh0cnVlKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINmB2Yog2KfZhNiq2LPYrNmK2YQnXG4gICAgICB9O1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dvdXQgPSAoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndG9rZW4nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgfVxuICAgIHNldFVzZXIobnVsbCk7XG4gICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVQcm9maWxlID0gYXN5bmMgKHByb2ZpbGVEYXRhKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnB1dCgnL2F1dGgvcHJvZmlsZScsIHByb2ZpbGVEYXRhKTtcbiAgICAgIGNvbnN0IHVwZGF0ZWRVc2VyID0gcmVzcG9uc2UuZGF0YS51c2VyO1xuICAgICAgXG4gICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyJywgSlNPTi5zdHJpbmdpZnkodXBkYXRlZFVzZXIpKTtcbiAgICAgIFxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAn2YHYtNmEINmB2Yog2KrYrdiv2YrYqyDYp9mE2KjZitin2YbYp9iqJ1xuICAgICAgfTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgdXNlcixcbiAgICBpc0F1dGhlbnRpY2F0ZWQsXG4gICAgbG9hZGluZyxcbiAgICBsb2dpbixcbiAgICByZWdpc3RlcixcbiAgICBsb2dvdXQsXG4gICAgdXBkYXRlUHJvZmlsZSxcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb25zXG4gICAgaXNTdHVkZW50OiB1c2VyPy5yb2xlID09PSAnc3R1ZGVudCcsXG4gICAgaXNBZG1pbjogdXNlcj8ucm9sZSA9PT0gJ2FkbWluJyxcbiAgICBpc1N1cGVyQWRtaW46IHVzZXI/LnJvbGUgPT09ICdzdXBlci1hZG1pbicsXG4gICAgaXNBZG1pbk9yU3VwZXJBZG1pbjogWydhZG1pbicsICdzdXBlci1hZG1pbiddLmluY2x1ZGVzKHVzZXI/LnJvbGUpLFxuICAgIC8vIEFQSSBpbnN0YW5jZVxuICAgIGFwaVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImF4aW9zIiwiQXV0aENvbnRleHQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIiwiYXBpIiwiY3JlYXRlIiwiYmFzZVVSTCIsImludGVyY2VwdG9ycyIsInJlcXVlc3QiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoZWFkZXJzIiwiQXV0aG9yaXphdGlvbiIsInJlc3BvbnNlIiwiZXJyb3IiLCJzdGF0dXMiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiUHJvbWlzZSIsInJlamVjdCIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwidXNlciIsInNldFVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImlzQXV0aGVudGljYXRlZCIsInNldElzQXV0aGVudGljYXRlZCIsImluaXRBdXRoIiwibG9jYWxVc2VyIiwicGFyc2VkVXNlciIsIkpTT04iLCJwYXJzZSIsImdldCIsImRhdGEiLCJjb25zb2xlIiwibG9nb3V0IiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicG9zdCIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJzdWNjZXNzIiwibWVzc2FnZSIsInJlZ2lzdGVyIiwidXNlckRhdGEiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiLCJwdXQiLCJ1cGRhdGVkVXNlciIsInZhbHVlIiwiaXNTdHVkZW50Iiwicm9sZSIsImlzQWRtaW4iLCJpc1N1cGVyQWRtaW4iLCJpc0FkbWluT3JTdXBlckFkbWluIiwiaW5jbHVkZXMiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.js\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.js":
/*!**********************************!*\
  !*** ./contexts/ThemeContext.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من الثيم المحفوظ أو تفضيل النظام\n        const savedTheme = localStorage.getItem(\"theme\");\n        if (savedTheme) {\n            setIsDark(savedTheme === \"dark\");\n        } else {\n            setIsDark(window.matchMedia(\"(prefers-color-scheme: dark)\").matches);\n        }\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const theme = isDark ? \"dark\" : \"light\";\n        localStorage.setItem(\"theme\", theme);\n        document.body.setAttribute(\"data-bs-theme\", theme);\n        document.body.className = theme === \"dark\" ? \"bg-dark text-light\" : \"bg-light text-dark\";\n        // إضافة transition للتغيير السلس\n        document.documentElement.style.transition = \"background-color 0.3s, color 0.3s\";\n    }, [\n        isDark,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setIsDark((prev)=>!prev);\n    };\n    const value = {\n        isDark,\n        toggleTheme,\n        theme: isDark ? \"dark\" : \"light\",\n        mounted\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-bolt-academy\\\\project\\\\academy-nextjs\\\\contexts\\\\ThemeContext.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.js\n");

/***/ }),

/***/ "(ssr)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"025f4d4086cc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hY2FkZW15LW5leHRqcy8uL2FwcC9nbG9iYWxzLmNzcz8xYmY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDI1ZjRkNDA4NmNjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\layout.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/my-courses/page.js":
/*!********************************!*\
  !*** ./app/my-courses/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\project-bolt-academy\project\academy-nextjs\app\my-courses\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/@restart","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/react-bootstrap","vendor-chunks/react-toastify","vendor-chunks/prop-types","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/react-transition-group","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/uncontrollable","vendor-chunks/@react-aria","vendor-chunks/dom-helpers","vendor-chunks/react-is","vendor-chunks/asynckit","vendor-chunks/react-lifecycles-compat","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/dequal","vendor-chunks/object-assign","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/warning","vendor-chunks/classnames","vendor-chunks/invariant","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/@babel","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/bootstrap","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmy-courses%2Fpage&page=%2Fmy-courses%2Fpage&appPaths=%2Fmy-courses%2Fpage&pagePath=private-next-app-dir%2Fmy-courses%2Fpage.js&appDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbdulmnoum%5CDownloads%5Cproject-bolt-academy%5Cproject%5Cacademy-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();