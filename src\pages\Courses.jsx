import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { coursesAPI } from '../services/api';
import { useTheme } from '../context/ThemeContext';
import { API_CONFIG } from '../config/api';
import { Search, Play, Clock, User } from 'lucide-react';

const Courses = () => {
  const { isDark } = useTheme();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0
  });

  // تحميل الدورات من الباكيند فقط
  const fetchCourses = async (searchQuery = '', page = 1) => {
    try {
      setLoading(true);
      const response = await coursesAPI.getAll({
        search: searchQuery,
        page,
        limit: 6
      });
      setCourses(response.data.courses);
      setPagination({
        currentPage: response.data.currentPage,
        totalPages: response.data.totalPages,
        total: response.data.total
      });
    } catch (err) {
      setError('فشل في تحميل الدورات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    fetchCourses(search, 1);
  };

  const handlePageChange = (page) => {
    fetchCourses(search, page);
  };

  const getLevelBadgeVariant = (level) => {
    switch (level) {
      case 'مبتدئ': return 'success';
      case 'متوسط': return 'warning';
      case 'متقدم': return 'danger';
      case 'Beginner': return 'success';
      case 'Intermediate': return 'warning';
      case 'Advanced': return 'danger';
      default: return 'Beginner';
    }
  };

  return (
    <Container className="py-5" dir="rtl">
      <Row className="mb-5">
        <Col>
          <h1 className="fw-bold mb-3">استكشف الدورات</h1>
          <p className="text-muted mb-4">
            اكتشف مجموعتنا الشاملة من الدورات المصممة لمساعدتك على إتقان مهارات جديدة.
          </p>
          
          <Form onSubmit={handleSearch} className="mb-4">
            <Row>
              <Col md={8} lg={9}>
                <Form.Control
                  type="text"
                  placeholder="البحث في الدورات..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </Col>
              <Col md={4} lg={3}>
                <Button type="submit" variant="primary" className="w-100">
                  <Search size={16} className="me-1" />
                  بحث
                </Button>
              </Col>
            </Row>
          </Form>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">جاري التحميل...</span>
          </div>
        </div>
      ) : (
        <>
          <Row className="g-4 mb-4">
            {courses.map((course) => (
              <Col key={course._id} md={6} lg={4}>
                <Card className={`h-100 shadow-sm border-0 hover-shadow ${isDark ? 'bg-dark text-light' : 'bg-white'}`}>
                  <div className="position-relative">
                    {course.image ? (
                      <div className="position-relative">
                        <img
                          src={`${API_CONFIG.BASE_URL}/${course.image}`}
                          alt={course.title}
                          className="w-100"
                          style={{ height: '200px', objectFit: 'cover' }}
                        />
                        <div
                          className="position-absolute top-50 start-50 translate-middle"
                          style={{ backgroundColor: 'rgba(0,0,0,0.5)', borderRadius: '50%', padding: '15px' }}
                        >
                          <Play size={32} className="text-white" />
                        </div>
                      </div>
                    ) : (
                      <div
                        className={`${isDark ? 'bg-info' : 'bg-primary'} d-flex align-items-center justify-content-center`}
                        style={{ height: '200px' }}
                      >
                        <div className="text-center text-white">
                          <Play size={48} className="mb-2" />
                          <h6>{course.title}</h6>
                        </div>
                      </div>
                    )}
                    <Badge
                      bg={getLevelBadgeVariant(course.level)}
                      className="position-absolute top-0 start-0 m-3"
                    >
                      {course.level}
                    </Badge>
                  </div>
                  
                  <Card.Body className="d-flex flex-column">
                    <Card.Title className="fw-bold">{course.title}</Card.Title>
                    <Card.Text className="text-muted flex-grow-1">
                      {course.description.length > 100 
                        ? course.description.substring(0, 100) + '...'
                        : course.description
                      }
                    </Card.Text>
                    
                    <div className="d-flex justify-content-between align-items-center mb-3 text-muted small">
                      <div className="d-flex align-items-center">
                        <User size={14} className="me-1" />
                        {course.instructor}
                      </div>
                      <div className="d-flex align-items-center">
                        <Clock size={14} className="me-1" />
                        {course.duration}
                      </div>
                    </div>
                    
                    <Button 
                      as={Link} 
                      to={`/courses/${course._id}`} 
                      variant={isDark ? "info" : "primary"}
                      className="w-100"
                    >
                      عرض الدورة
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>

          {courses.length === 0 && !loading && (
            <div className="text-center py-5">
              <h4 className="text-muted">لم يتم العثور على دورات</h4>
              <p className="text-muted">جرب تعديل مصطلحات البحث</p>
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <Row className="mt-5">
              <Col className="d-flex justify-content-center">
                <div className="d-flex gap-2">
                  <Button
                    variant="outline-primary"
                    disabled={pagination.currentPage === 1}
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                  >
                    السابق
                  </Button>
                  
                  {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                    .slice(
                      Math.max(0, pagination.currentPage - 3),
                      Math.min(pagination.totalPages, pagination.currentPage + 2)
                    )
                    .map(page => (
                      <Button
                        key={page}
                        variant={page === pagination.currentPage ? "primary" : "outline-primary"}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    ))}
                  
                  <Button
                    variant="outline-primary"
                    disabled={pagination.currentPage === pagination.totalPages}
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                  >
                    التالي
                  </Button>
                </div>
              </Col>
            </Row>
          )}
        </>
      )}
    </Container>
  );
};

export default Courses;