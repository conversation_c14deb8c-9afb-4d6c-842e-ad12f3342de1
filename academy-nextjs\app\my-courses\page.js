'use client';

import { useState, useEffect } from 'react';
import { Container, Row, Col, Card, ProgressBar, Badge, Button, Tab, Tabs } from 'react-bootstrap';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  BookOpen, 
  Play, 
  CheckCircle, 
  Clock, 
  Star, 
  Award,
  TrendingUp,
  Calendar,
  Target,
  User
} from 'lucide-react';

export default function MyCourses() {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { isDark } = useTheme();
  const router = useRouter();
  
  const [activeTab, setActiveTab] = useState('in-progress');
  const [courses, setCourses] = useState({
    inProgress: [],
    completed: [],
    wishlist: []
  });
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCourses: 0,
    completedCourses: 0,
    totalHours: 0,
    certificates: 0
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (isAuthenticated) {
      fetchMyCourses();
    }
  }, [isAuthenticated, authLoading, router]);

  const fetchMyCourses = async () => {
    try {
      setLoading(true);
      
      // محاكاة بيانات دورات المستخدم
      const mockCourses = {
        inProgress: [
          {
            _id: '1',
            title: 'تطوير تطبيقات React المتقدمة',
            instructor: 'أحمد المطور',
            progress: 65,
            totalLessons: 24,
            completedLessons: 16,
            lastAccessed: new Date(),
            estimatedTime: '8 ساعات متبقية',
            level: 'متوسط',
            category: 'البرمجة'
          },
          {
            _id: '2',
            title: 'تصميم واجهات المستخدم',
            instructor: 'سارة المصممة',
            progress: 30,
            totalLessons: 18,
            completedLessons: 5,
            lastAccessed: new Date(Date.now() - 86400000),
            estimatedTime: '12 ساعة متبقية',
            level: 'مبتدئ',
            category: 'التصميم'
          },
          {
            _id: '3',
            title: 'أساسيات الأمن السيبراني',
            instructor: 'محمد الخبير',
            progress: 85,
            totalLessons: 15,
            completedLessons: 13,
            lastAccessed: new Date(Date.now() - 172800000),
            estimatedTime: '2 ساعة متبقية',
            level: 'متقدم',
            category: 'الأمن السيبراني'
          }
        ],
        completed: [
          {
            _id: '4',
            title: 'أساسيات JavaScript',
            instructor: 'علي المبرمج',
            completedAt: new Date(Date.now() - 604800000),
            rating: 5,
            certificate: true,
            totalHours: 15,
            level: 'مبتدئ',
            category: 'البرمجة'
          },
          {
            _id: '5',
            title: 'مقدمة في قواعد البيانات',
            instructor: 'فاطمة الخبيرة',
            completedAt: new Date(Date.now() - 1209600000),
            rating: 4,
            certificate: true,
            totalHours: 12,
            level: 'مبتدئ',
            category: 'قواعد البيانات'
          }
        ],
        wishlist: [
          {
            _id: '6',
            title: 'تطوير تطبيقات الهاتف المحمول',
            instructor: 'خالد المطور',
            price: 399,
            rating: 4.8,
            students: 1200,
            level: 'متقدم',
            category: 'تطوير التطبيقات'
          }
        ]
      };

      setCourses(mockCourses);

      // حساب الإحصائيات
      setStats({
        totalCourses: mockCourses.inProgress.length + mockCourses.completed.length,
        completedCourses: mockCourses.completed.length,
        totalHours: mockCourses.completed.reduce((total, course) => total + course.totalHours, 0),
        certificates: mockCourses.completed.filter(course => course.certificate).length
      });

    } catch (error) {
      console.error('Error fetching my courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const getLevelBadge = (level) => {
    switch (level) {
      case 'مبتدئ':
        return <Badge bg="success">مبتدئ</Badge>;
      case 'متوسط':
        return <Badge bg="warning">متوسط</Badge>;
      case 'متقدم':
        return <Badge bg="danger">متقدم</Badge>;
      default:
        return <Badge bg="secondary">{level}</Badge>;
    }
  };

  const formatLastAccessed = (date) => {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'اليوم';
    if (days === 1) return 'أمس';
    return `منذ ${days} أيام`;
  };

  if (authLoading || loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="spinner-border text-primary" />
      </div>
    );
  }

  return (
    <div className={`min-vh-100 ${isDark ? 'bg-dark text-light' : 'bg-light'}`}>
      <Container className="py-5">
        {/* Header */}
        <Row className="mb-5">
          <Col>
            <div className="text-center">
              <h1 className="display-5 fw-bold mb-3">دوراتي</h1>
              <p className="lead text-muted">
                تابع تقدمك في الدورات واستكمل رحلتك التعليمية
              </p>
            </div>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <BookOpen size={32} className="text-primary mb-2" />
                <h3 className="fw-bold">{stats.totalCourses}</h3>
                <p className="text-muted mb-0">إجمالي الدورات</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <CheckCircle size={32} className="text-success mb-2" />
                <h3 className="fw-bold">{stats.completedCourses}</h3>
                <p className="text-muted mb-0">دورة مكتملة</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Clock size={32} className="text-info mb-2" />
                <h3 className="fw-bold">{stats.totalHours}</h3>
                <p className="text-muted mb-0">ساعة تعلم</p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className={`text-center border-0 shadow-sm h-100 ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Award size={32} className="text-warning mb-2" />
                <h3 className="fw-bold">{stats.certificates}</h3>
                <p className="text-muted mb-0">شهادة</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Course Tabs */}
        <Row>
          <Col>
            <Card className={`border-0 shadow-sm ${isDark ? 'bg-dark text-light' : ''}`}>
              <Card.Body>
                <Tabs
                  activeKey={activeTab}
                  onSelect={setActiveTab}
                  className="mb-4"
                >
                  <Tab eventKey="in-progress" title={`قيد التقدم (${courses.inProgress.length})`}>
                    <Row className="g-4">
                      {courses.inProgress.map((course) => (
                        <Col md={6} lg={4} key={course._id}>
                          <Card className={`h-100 border-0 shadow-sm ${isDark ? 'bg-secondary' : 'bg-white'}`}>
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-3">
                                <Badge bg="primary">{course.category}</Badge>
                                {getLevelBadge(course.level)}
                              </div>
                              
                              <h5 className="fw-bold mb-2">{course.title}</h5>
                              
                              <div className="d-flex align-items-center mb-3">
                                <User size={14} className="text-muted me-1" />
                                <small className="text-muted">{course.instructor}</small>
                              </div>

                              <div className="mb-3">
                                <div className="d-flex justify-content-between align-items-center mb-2">
                                  <span className="small">التقدم</span>
                                  <span className="small fw-bold">{course.progress}%</span>
                                </div>
                                <ProgressBar 
                                  now={course.progress} 
                                  variant={course.progress > 80 ? 'success' : course.progress > 50 ? 'primary' : 'warning'}
                                />
                                <small className="text-muted">
                                  {course.completedLessons} من {course.totalLessons} درس
                                </small>
                              </div>

                              <div className="d-flex justify-content-between align-items-center mb-3">
                                <small className="text-muted">
                                  <Calendar size={12} className="me-1" />
                                  آخر وصول: {formatLastAccessed(course.lastAccessed)}
                                </small>
                                <small className="text-info">
                                  <Clock size={12} className="me-1" />
                                  {course.estimatedTime}
                                </small>
                              </div>

                              <div className="d-grid">
                                <Link href={`/courses/${course._id}`}>
                                  <Button variant="primary">
                                    <Play size={16} className="me-1" />
                                    متابعة التعلم
                                  </Button>
                                </Link>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Tab>

                  <Tab eventKey="completed" title={`مكتملة (${courses.completed.length})`}>
                    <Row className="g-4">
                      {courses.completed.map((course) => (
                        <Col md={6} lg={4} key={course._id}>
                          <Card className={`h-100 border-0 shadow-sm ${isDark ? 'bg-secondary' : 'bg-white'}`}>
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-3">
                                <Badge bg="success">مكتملة</Badge>
                                {course.certificate && (
                                  <Award size={20} className="text-warning" />
                                )}
                              </div>
                              
                              <h5 className="fw-bold mb-2">{course.title}</h5>
                              
                              <div className="d-flex align-items-center mb-3">
                                <User size={14} className="text-muted me-1" />
                                <small className="text-muted">{course.instructor}</small>
                              </div>

                              <div className="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                  <small className="text-muted d-block">تاريخ الإكمال</small>
                                  <small>{course.completedAt.toLocaleDateString('ar-SA')}</small>
                                </div>
                                <div className="text-end">
                                  <small className="text-muted d-block">المدة</small>
                                  <small>{course.totalHours} ساعة</small>
                                </div>
                              </div>

                              <div className="d-flex align-items-center mb-3">
                                <span className="me-2">تقييمك:</span>
                                <div className="d-flex">
                                  {[...Array(5)].map((_, i) => (
                                    <Star 
                                      key={i} 
                                      size={14} 
                                      className={i < course.rating ? 'text-warning' : 'text-muted'} 
                                    />
                                  ))}
                                </div>
                              </div>

                              <div className="d-grid gap-2">
                                <Link href={`/courses/${course._id}`}>
                                  <Button variant="outline-primary">
                                    مراجعة الدورة
                                  </Button>
                                </Link>
                                {course.certificate && (
                                  <Button variant="success" size="sm">
                                    <Award size={14} className="me-1" />
                                    تحميل الشهادة
                                  </Button>
                                )}
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Tab>

                  <Tab eventKey="wishlist" title={`قائمة الأمنيات (${courses.wishlist.length})`}>
                    <Row className="g-4">
                      {courses.wishlist.map((course) => (
                        <Col md={6} lg={4} key={course._id}>
                          <Card className={`h-100 border-0 shadow-sm ${isDark ? 'bg-secondary' : 'bg-white'}`}>
                            <Card.Body>
                              <div className="d-flex justify-content-between align-items-start mb-3">
                                <Badge bg="info">{course.category}</Badge>
                                {getLevelBadge(course.level)}
                              </div>
                              
                              <h5 className="fw-bold mb-2">{course.title}</h5>
                              
                              <div className="d-flex align-items-center mb-3">
                                <User size={14} className="text-muted me-1" />
                                <small className="text-muted">{course.instructor}</small>
                              </div>

                              <div className="d-flex justify-content-between align-items-center mb-3">
                                <div className="d-flex align-items-center">
                                  <Star size={14} className="text-warning me-1" />
                                  <span>{course.rating}</span>
                                </div>
                                <small className="text-muted">
                                  {course.students} طالب
                                </small>
                              </div>

                              <div className="d-flex justify-content-between align-items-center mb-3">
                                <h5 className="fw-bold text-primary mb-0">
                                  {course.price} ر.س
                                </h5>
                              </div>

                              <div className="d-grid gap-2">
                                <Link href={`/courses/${course._id}`}>
                                  <Button variant="primary">
                                    عرض التفاصيل
                                  </Button>
                                </Link>
                                <Button variant="outline-danger" size="sm">
                                  إزالة من القائمة
                                </Button>
                              </div>
                            </Card.Body>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Tab>
                </Tabs>

                {/* Empty State */}
                {activeTab === 'in-progress' && courses.inProgress.length === 0 && (
                  <div className="text-center py-5">
                    <BookOpen size={64} className="text-muted mb-3" />
                    <h4>لا توجد دورات قيد التقدم</h4>
                    <p className="text-muted mb-4">ابدأ رحلتك التعليمية بالتسجيل في دورة جديدة</p>
                    <Link href="/courses">
                      <Button variant="primary">
                        تصفح الدورات
                      </Button>
                    </Link>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
